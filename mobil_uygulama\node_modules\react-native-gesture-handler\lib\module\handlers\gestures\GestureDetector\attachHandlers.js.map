{"version": 3, "sources": ["attachHandlers.ts"], "names": ["registerHandler", "RNGestureHandlerModule", "filterConfig", "scheduleFlushOperations", "ActionType", "Platform", "ghQueueMicrotask", "extractGestureRelations", "checkGestureCallbacksForWorklets", "ALLOWED_PROPS", "attachHandlers", "preparedGesture", "gestureConfig", "gestures<PERSON>oAtta<PERSON>", "viewTag", "webEventHandlersRef", "initialize", "isMounted", "prepare", "handler", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "testId", "updateGestureHandler", "gesture", "actionType", "shouldUseReanimated", "REANIMATED_WORKLET", "JS_FUNCTION_NEW_API", "OS", "attachGestureHandler", "JS_FUNCTION_OLD_API", "attachedGestures", "animatedHandlers", "isAnimatedGesture", "g", "value", "filter", "map", "handlers"], "mappings": "AAEA,SAASA,eAAT,QAAgC,wBAAhC;AACA,OAAOC,sBAAP,MAAmC,iCAAnC;AACA,SAASC,YAAT,EAAuBC,uBAAvB,QAAsD,aAAtD;AAEA,SAASC,UAAT,QAA2B,qBAA3B;AACA,SAASC,QAAT,QAAyB,cAAzB;AAEA,SAASC,gBAAT,QAAiC,2BAAjC;AAEA,SACEC,uBADF,EAEEC,gCAFF,EAGEC,aAHF,QAIO,SAJP;AAcA,OAAO,SAASC,cAAT,CAAwB;AAC7BC,EAAAA,eAD6B;AAE7BC,EAAAA,aAF6B;AAG7BC,EAAAA,gBAH6B;AAI7BC,EAAAA,OAJ6B;AAK7BC,EAAAA;AAL6B,CAAxB,EAMkB;AACvBH,EAAAA,aAAa,CAACI,UAAd,GADuB,CAGvB;AACA;;AACAV,EAAAA,gBAAgB,CAAC,MAAM;AACrB,QAAI,CAACK,eAAe,CAACM,SAArB,EAAgC;AAC9B;AACD;;AACDL,IAAAA,aAAa,CAACM,OAAd;AACD,GALe,CAAhB;;AAOA,OAAK,MAAMC,OAAX,IAAsBN,gBAAtB,EAAwC;AACtCL,IAAAA,gCAAgC,CAACW,OAAD,CAAhC;AACAlB,IAAAA,sBAAsB,CAACmB,oBAAvB,CACED,OAAO,CAACE,WADV,EAEEF,OAAO,CAACG,UAFV,EAGEpB,YAAY,CAACiB,OAAO,CAACI,MAAT,EAAiBd,aAAjB,CAHd;AAMAT,IAAAA,eAAe,CAACmB,OAAO,CAACG,UAAT,EAAqBH,OAArB,EAA8BA,OAAO,CAACI,MAAR,CAAeC,MAA7C,CAAf;AACD,GArBsB,CAuBvB;AACA;;;AACAlB,EAAAA,gBAAgB,CAAC,MAAM;AACrB,QAAI,CAACK,eAAe,CAACM,SAArB,EAAgC;AAC9B;AACD;;AACD,SAAK,MAAME,OAAX,IAAsBN,gBAAtB,EAAwC;AACtCZ,MAAAA,sBAAsB,CAACwB,oBAAvB,CACEN,OAAO,CAACG,UADV,EAEEpB,YAAY,CACViB,OAAO,CAACI,MADE,EAEVd,aAFU,EAGVF,uBAAuB,CAACY,OAAD,CAHb,CAFd;AAQD;;AAEDhB,IAAAA,uBAAuB;AACxB,GAhBe,CAAhB;;AAkBA,OAAK,MAAMuB,OAAX,IAAsBb,gBAAtB,EAAwC;AACtC,UAAMc,UAAU,GAAGD,OAAO,CAACE,mBAAR,GACfxB,UAAU,CAACyB,kBADI,GAEfzB,UAAU,CAAC0B,mBAFf;;AAIA,QAAIzB,QAAQ,CAAC0B,EAAT,KAAgB,KAApB,EAA2B;AAEvB9B,MAAAA,sBAAsB,CAAC+B,oBADzB,CAGEN,OAAO,CAACJ,UAHV,EAIER,OAJF,EAKEV,UAAU,CAAC6B,mBALb,EAKkC;AAChClB,MAAAA,mBANF;AAQD,KATD,MASO;AACLd,MAAAA,sBAAsB,CAAC+B,oBAAvB,CACEN,OAAO,CAACJ,UADV,EAEER,OAFF,EAGEa,UAHF;AAKD;AACF;;AAEDhB,EAAAA,eAAe,CAACuB,gBAAhB,GAAmCrB,gBAAnC;;AAEA,MAAIF,eAAe,CAACwB,gBAApB,EAAsC;AACpC,UAAMC,iBAAiB,GAAIC,CAAD,IAAoBA,CAAC,CAACT,mBAAhD;;AAEAjB,IAAAA,eAAe,CAACwB,gBAAhB,CAAiCG,KAAjC,GAAyCzB,gBAAgB,CACtD0B,MADsC,CAC/BH,iBAD+B,EAEtCI,GAFsC,CAEjCH,CAAD,IAAOA,CAAC,CAACI,QAFyB,CAAzC;AAKD;AACF", "sourcesContent": ["import React from 'react';\nimport { GestureType, HandlerCallbacks } from '../gesture';\nimport { registerHandler } from '../../handlersRegistry';\nimport RNGestureHandlerModule from '../../../RNGestureHandlerModule';\nimport { filterConfig, scheduleFlushOperations } from '../../utils';\nimport { ComposedGesture } from '../gestureComposition';\nimport { ActionType } from '../../../ActionType';\nimport { Platform } from 'react-native';\nimport type RNGestureHandlerModuleWeb from '../../../RNGestureHandlerModule.web';\nimport { ghQueueMicrotask } from '../../../ghQueueMicrotask';\nimport { AttachedGestureState, WebEventHandler } from './types';\nimport {\n  extractGestureRelations,\n  checkGestureCallbacksForWorklets,\n  ALLOWED_PROPS,\n} from './utils';\n\ninterface AttachHandlersConfig {\n  preparedGesture: AttachedGestureState;\n  gestureConfig: ComposedGesture | GestureType;\n  gesturesToAttach: GestureType[];\n  viewTag: number;\n  webEventHandlersRef: React.RefObject<WebEventHandler>;\n}\n\nexport function attachHandlers({\n  preparedGesture,\n  gestureConfig,\n  gesturesToAttach,\n  viewTag,\n  webEventHandlersRef,\n}: AttachHandlersConfig) {\n  gestureConfig.initialize();\n\n  // Use queueMicrotask to extract handlerTags, because all refs should be initialized\n  // when it's ran\n  ghQueueMicrotask(() => {\n    if (!preparedGesture.isMounted) {\n      return;\n    }\n    gestureConfig.prepare();\n  });\n\n  for (const handler of gesturesToAttach) {\n    checkGestureCallbacksForWorklets(handler);\n    RNGestureHandlerModule.createGestureHandler(\n      handler.handlerName,\n      handler.handlerTag,\n      filterConfig(handler.config, ALLOWED_PROPS)\n    );\n\n    registerHandler(handler.handlerTag, handler, handler.config.testId);\n  }\n\n  // Use queueMicrotask to extract handlerTags, because all refs should be initialized\n  // when it's ran\n  ghQueueMicrotask(() => {\n    if (!preparedGesture.isMounted) {\n      return;\n    }\n    for (const handler of gesturesToAttach) {\n      RNGestureHandlerModule.updateGestureHandler(\n        handler.handlerTag,\n        filterConfig(\n          handler.config,\n          ALLOWED_PROPS,\n          extractGestureRelations(handler)\n        )\n      );\n    }\n\n    scheduleFlushOperations();\n  });\n\n  for (const gesture of gesturesToAttach) {\n    const actionType = gesture.shouldUseReanimated\n      ? ActionType.REANIMATED_WORKLET\n      : ActionType.JS_FUNCTION_NEW_API;\n\n    if (Platform.OS === 'web') {\n      (\n        RNGestureHandlerModule.attachGestureHandler as typeof RNGestureHandlerModuleWeb.attachGestureHandler\n      )(\n        gesture.handlerTag,\n        viewTag,\n        ActionType.JS_FUNCTION_OLD_API, // Ignored on web\n        webEventHandlersRef\n      );\n    } else {\n      RNGestureHandlerModule.attachGestureHandler(\n        gesture.handlerTag,\n        viewTag,\n        actionType\n      );\n    }\n  }\n\n  preparedGesture.attachedGestures = gesturesToAttach;\n\n  if (preparedGesture.animatedHandlers) {\n    const isAnimatedGesture = (g: GestureType) => g.shouldUseReanimated;\n\n    preparedGesture.animatedHandlers.value = gesturesToAttach\n      .filter(isAnimatedGesture)\n      .map((g) => g.handlers) as unknown as HandlerCallbacks<\n      Record<string, unknown>\n    >[];\n  }\n}\n"]}