{"version": 3, "file": "shimmer.js", "sourceRoot": "", "sources": ["../../src/shimmer.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAqCH,wEAAwE;AACxE,sCAAsC;AACtC,IAAI,MAAM,GAAyB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAE/D,8DAA8D;AAC9D,+DAA+D;AAC/D,SAAS,cAAc,CAAC,GAAW,EAAE,IAAiB,EAAE,KAAc;IACpE,MAAM,UAAU,GACd,CAAC,CAAC,GAAG,CAAC,IAAwB,CAAC;QAC/B,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAExD,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE;QAC/B,YAAY,EAAE,IAAI;QAClB,UAAU;QACV,QAAQ,EAAE,IAAI;QACd,KAAK;KACN,CAAC,CAAC;AACL,CAAC;AAED,MAAM,CAAC,MAAM,IAAI,GAAG,CAClB,MAAc,EACd,IAAe,EACf,OAA4E,EACnD,EAAE;IAC3B,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QAC5B,MAAM,CAAC,uBAAuB,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;QAC5D,OAAO;KACR;IAED,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,CAAC,qBAAqB,CAAC,CAAC;QAC9B,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;QAC1B,OAAO;KACR;IAED,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IAE9B,IAAI,OAAO,QAAQ,KAAK,UAAU,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;QACnE,MAAM,CAAC,+CAA+C,CAAC,CAAC;QACxD,OAAO;KACR;IAED,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAW,CAAC;IAElD,cAAc,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;IAChD,cAAc,CAAC,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE;QACvC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,EAAE;YAC5B,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;SACxC;IACH,CAAC,CAAC,CAAC;IACH,cAAc,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;IAC3C,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACtC,OAAO,OAAsB,CAAC;AAChC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG,CACtB,OAAiB,EACjB,KAAkB,EAClB,OAA2D,EACrD,EAAE;IACR,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,CAAC,2CAA2C,CAAC,CAAC;QACpD,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;QAC1B,OAAO;KACR;SAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAClC,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;KACrB;IAED,IAAI,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QACpC,MAAM,CAAC,uDAAuD,CAAC,CAAC;QAChE,OAAO;KACR;IAED,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAAG,CACpB,MAAc,EACd,IAAkB,EACZ,EAAE;IACR,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QAC5B,MAAM,CAAC,wBAAwB,CAAC,CAAC;QACjC,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;QAC1B,OAAO;KACR;IAED,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAA2B,CAAC;IAEvD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;QACrB,MAAM,CACJ,kCAAkC;YAChC,MAAM,CAAC,IAAI,CAAC;YACZ,0BAA0B,CAC7B,CAAC;KACH;SAAM;QACL,OAAO,CAAC,QAAQ,EAAE,CAAC;QACnB,OAAO;KACR;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAG,CACxB,OAAiB,EACjB,KAA0B,EACpB,EAAE;IACR,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,CAAC,2CAA2C,CAAC,CAAC;QACpD,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;QAC1B,OAAO;KACR;SAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAClC,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;KACrB;IAED,IAAI,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QACpC,MAAM,CAAC,yDAAyD,CAAC,CAAC;QAClE,OAAO;KACR;IAED,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAMF,MAAM,CAAC,OAAO,UAAU,OAAO,CAAC,OAAuB;IACrD,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;QAC7B,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,UAAU,EAAE;YACxC,MAAM,CAAC,4CAA4C,CAAC,CAAC;SACtD;aAAM;YACL,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;SACzB;KACF;AACH,CAAC;AAED,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;AACpB,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC5B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AACxB,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * BSD 2-Clause License\n *\n * Copyright (c) 2013-2019, <PERSON>vell\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * * Redistributions of source code must retain the above copyright notice, this\n *   list of conditions and the following disclaimer.\n *\n * * Redistributions in binary form must reproduce the above copyright notice,\n *   this list of conditions and the following disclaimer in the documentation\n *   and/or other materials provided with the distribution.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\n * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\n * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\n * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n/* Modified by OpenTelemetry Authors\n *  - converted to TypeScript\n *  - aligned with style-guide\n */\n\nimport { ShimWrapped } from './types';\n\n// Default to complaining loudly when things don't go according to plan.\n// eslint-disable-next-line no-console\nlet logger: typeof console.error = console.error.bind(console);\n\n// Sets a property on an object, preserving its enumerability.\n// This function assumes that the property is already writable.\nfunction defineProperty(obj: object, name: PropertyKey, value: unknown): void {\n  const enumerable =\n    !!obj[name as keyof typeof obj] &&\n    Object.prototype.propertyIsEnumerable.call(obj, name);\n\n  Object.defineProperty(obj, name, {\n    configurable: true,\n    enumerable,\n    writable: true,\n    value,\n  });\n}\n\nexport const wrap = <Nodule extends object, FieldName extends keyof Nodule>(\n  nodule: Nodule,\n  name: FieldName,\n  wrapper: (original: Nodule[FieldName], name: FieldName) => Nodule[FieldName]\n): ShimWrapped | undefined => {\n  if (!nodule || !nodule[name]) {\n    logger('no original function ' + String(name) + ' to wrap');\n    return;\n  }\n\n  if (!wrapper) {\n    logger('no wrapper function');\n    logger(new Error().stack);\n    return;\n  }\n\n  const original = nodule[name];\n\n  if (typeof original !== 'function' || typeof wrapper !== 'function') {\n    logger('original object and wrapper must be functions');\n    return;\n  }\n\n  const wrapped = wrapper(original, name) as object;\n\n  defineProperty(wrapped, '__original', original);\n  defineProperty(wrapped, '__unwrap', () => {\n    if (nodule[name] === wrapped) {\n      defineProperty(nodule, name, original);\n    }\n  });\n  defineProperty(wrapped, '__wrapped', true);\n  defineProperty(nodule, name, wrapped);\n  return wrapped as ShimWrapped;\n};\n\nexport const massWrap = <Nodule extends object, FieldName extends keyof Nodule>(\n  nodules: Nodule[],\n  names: FieldName[],\n  wrapper: (original: Nodule[FieldName]) => Nodule[FieldName]\n): void => {\n  if (!nodules) {\n    logger('must provide one or more modules to patch');\n    logger(new Error().stack);\n    return;\n  } else if (!Array.isArray(nodules)) {\n    nodules = [nodules];\n  }\n\n  if (!(names && Array.isArray(names))) {\n    logger('must provide one or more functions to wrap on modules');\n    return;\n  }\n\n  nodules.forEach(nodule => {\n    names.forEach(name => {\n      wrap(nodule, name, wrapper);\n    });\n  });\n};\n\nexport const unwrap = <Nodule extends object>(\n  nodule: Nodule,\n  name: keyof Nodule\n): void => {\n  if (!nodule || !nodule[name]) {\n    logger('no function to unwrap.');\n    logger(new Error().stack);\n    return;\n  }\n\n  const wrapped = nodule[name] as unknown as ShimWrapped;\n\n  if (!wrapped.__unwrap) {\n    logger(\n      'no original to unwrap to -- has ' +\n        String(name) +\n        ' already been unwrapped?'\n    );\n  } else {\n    wrapped.__unwrap();\n    return;\n  }\n};\n\nexport const massUnwrap = <Nodule extends object>(\n  nodules: Nodule[],\n  names: Array<keyof Nodule>\n): void => {\n  if (!nodules) {\n    logger('must provide one or more modules to patch');\n    logger(new Error().stack);\n    return;\n  } else if (!Array.isArray(nodules)) {\n    nodules = [nodules];\n  }\n\n  if (!(names && Array.isArray(names))) {\n    logger('must provide one or more functions to unwrap on modules');\n    return;\n  }\n\n  nodules.forEach(nodule => {\n    names.forEach(name => {\n      unwrap(nodule, name);\n    });\n  });\n};\n\nexport interface ShimmerOptions {\n  logger?: typeof console.error;\n}\n\nexport default function shimmer(options: ShimmerOptions): void {\n  if (options && options.logger) {\n    if (typeof options.logger !== 'function') {\n      logger(\"new logger isn't a function, not replacing\");\n    } else {\n      logger = options.logger;\n    }\n  }\n}\n\nshimmer.wrap = wrap;\nshimmer.massWrap = massWrap;\nshimmer.unwrap = unwrap;\nshimmer.massUnwrap = massUnwrap;\n"]}