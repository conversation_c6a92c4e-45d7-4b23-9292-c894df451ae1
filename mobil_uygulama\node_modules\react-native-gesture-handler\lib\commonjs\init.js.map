{"version": 3, "sources": ["init.ts"], "names": ["fabricInitialized", "initialize", "maybeInitializeFabric", "RNGestureHandlerModule", "install"], "mappings": ";;;;;;;;AAAA;;AACA;;AACA;;;;AAEA,IAAIA,iBAAiB,GAAG,KAAxB;;AAEO,SAASC,UAAT,GAAsB;AAC3B;AACD,C,CAED;AACA;;;AACO,SAASC,qBAAT,GAAiC;AACtC,MAAI,0BAAc,CAACF,iBAAnB,EAAsC;AACpCG,oCAAuBC,OAAvB;;AACAJ,IAAAA,iBAAiB,GAAG,IAApB;AACD;AACF", "sourcesContent": ["import { startListening } from './handlers/gestures/eventReceiver';\nimport RNGestureHandlerModule from './RNGestureHandlerModule';\nimport { isFabric } from './utils';\n\nlet fabricInitialized = false;\n\nexport function initialize() {\n  startListening();\n}\n\n// Since isFabric() may give wrong results before the first render, we call this\n// method during render of GestureHandlerRootView\nexport function maybeInitializeFabric() {\n  if (isFabric() && !fabricInitialized) {\n    RNGestureHandlerModule.install();\n    fabricInitialized = true;\n  }\n}\n"]}