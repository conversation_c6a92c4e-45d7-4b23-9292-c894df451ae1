"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "RNRenderer", {
  enumerable: true,
  get: function () {
    return _ReactNative.default;
  }
});

var _ReactNative = _interopRequireDefault(require("react-native/Libraries/Renderer/shims/ReactNative"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
//# sourceMappingURL=RNRenderer.js.map