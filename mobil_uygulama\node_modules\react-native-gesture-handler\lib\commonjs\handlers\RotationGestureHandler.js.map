{"version": 3, "sources": ["RotationGestureHandler.ts"], "names": ["rotationHandlerName", "RotationGestureHandler", "name", "allowedProps", "baseGestureHandlerProps", "config"], "mappings": ";;;;;;;AACA;;AACA;;;;AAQO,MAAMA,mBAAmB,GAAG,wBAA5B;;AAGP;AACO,MAAMC,sBAAsB,GAAG,4BAGpC;AACAC,EAAAA,IAAI,EAAEF,mBADN;AAEAG,EAAAA,YAAY,EAAEC,6CAFd;AAGAC,EAAAA,MAAM,EAAE;AAHR,CAHoC,CAA/B", "sourcesContent": ["import { RotationGestureHandlerEventPayload } from './GestureHandlerEventPayload';\nimport createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\nexport interface RotationGestureHandlerProps\n  extends BaseGestureHandlerProps<RotationGestureHandlerEventPayload> {}\n\nexport const rotationHandlerName = 'RotationGestureHandler';\n\nexport type RotationGestureHandler = typeof RotationGestureHandler;\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const RotationGestureHandler = createHandler<\n  RotationGestureHandlerProps,\n  RotationGestureHandlerEventPayload\n>({\n  name: rotationHandlerName,\n  allowedProps: baseGestureHandlerProps,\n  config: {},\n});\n"]}