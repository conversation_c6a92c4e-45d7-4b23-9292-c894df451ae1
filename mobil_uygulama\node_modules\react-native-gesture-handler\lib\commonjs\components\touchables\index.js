"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "TouchableNativeFeedback", {
  enumerable: true,
  get: function () {
    return _TouchableNativeFeedback.default;
  }
});
Object.defineProperty(exports, "TouchableWithoutFeedback", {
  enumerable: true,
  get: function () {
    return _TouchableWithoutFeedback.default;
  }
});
Object.defineProperty(exports, "TouchableOpacity", {
  enumerable: true,
  get: function () {
    return _TouchableOpacity.default;
  }
});
Object.defineProperty(exports, "TouchableHighlight", {
  enumerable: true,
  get: function () {
    return _TouchableHighlight.default;
  }
});

var _TouchableNativeFeedback = _interopRequireDefault(require("./TouchableNativeFeedback"));

var _TouchableWithoutFeedback = _interopRequireDefault(require("./TouchableWithoutFeedback"));

var _TouchableOpacity = _interopRequireDefault(require("./TouchableOpacity"));

var _TouchableHighlight = _interopRequireDefault(require("./TouchableHighlight"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
//# sourceMappingURL=index.js.map