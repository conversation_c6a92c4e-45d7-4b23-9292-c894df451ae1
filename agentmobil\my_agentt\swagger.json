{"openapi": "3.0.0", "info": {"title": "Mastra AI Agent API", "description": "AI Agent API powered by Mastra framework with OpenAI integration", "version": "1.0.0", "contact": {"name": "Mastra AI Agent", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}], "paths": {"/api/health": {"get": {"summary": "Health Check", "description": "Check if the API is running", "tags": ["System"], "responses": {"200": {"description": "API is healthy", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "OK"}, "timestamp": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}}}}}}}}, "/api/chat": {"post": {"summary": "Chat with AI Agent", "description": "Send a message to the AI agent and get a response", "tags": ["AI Agent"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["message"], "properties": {"message": {"type": "string", "description": "The message to send to the AI agent", "example": "Merhaba, nasılsın?", "minLength": 1, "maxLength": 500}}}}}}, "responses": {"200": {"description": "Successful response from AI agent", "content": {"application/json": {"schema": {"type": "object", "properties": {"response": {"type": "string", "description": "The AI agent's response", "example": "Merhaba! <PERSON>, teşekk<PERSON>r ederim. Size nasıl yardımcı olabilirim?"}, "success": {"type": "boolean", "example": true}}}}}}, "400": {"description": "Bad request - invalid message", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Geçerli bir mesaj g<PERSON>ilmedi"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Bir hata oluştu. Lütfen tekrar deneyin."}, "success": {"type": "boolean", "example": false}}}}}}}}}, "/api/agents": {"get": {"summary": "List Available Agents", "description": "Get a list of all available AI agents", "tags": ["AI Agent"], "responses": {"200": {"description": "List of available agents", "content": {"application/json": {"schema": {"type": "object", "properties": {"agents": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "example": "assistant"}, "description": {"type": "string", "example": "Türkçe konuşan yardımcı AI asistanı"}}}}}}}}}}}}}, "components": {"schemas": {"ChatMessage": {"type": "object", "required": ["message"], "properties": {"message": {"type": "string", "description": "The message to send to the AI agent", "minLength": 1, "maxLength": 500}}}, "ChatResponse": {"type": "object", "properties": {"response": {"type": "string", "description": "The AI agent's response"}, "success": {"type": "boolean"}}}, "ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "success": {"type": "boolean", "example": false}}}}}, "tags": [{"name": "System", "description": "System health and status endpoints"}, {"name": "AI Agent", "description": "AI agent interaction endpoints"}]}