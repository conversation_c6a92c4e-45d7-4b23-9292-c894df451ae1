{"name": "a0-project", "version": "1.0.0", "license": "0BSD", "private": true, "main": "index.ts", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web"}, "dependencies": {"@expo/vector-icons": "latest", "@react-native-async-storage/async-storage": "latest", "@react-native-community/slider": "4.5.7", "@react-navigation/bottom-tabs": "7.3.13", "@react-navigation/native": "7.1.9", "@react-navigation/native-stack": "7.3.13", "@supabase/supabase-js": "2.49.8", "expo": "^52.0.42", "expo-camera": "16.1.6", "expo-linear-gradient": "14.1.4", "lucide-react-native": "0.511.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-gesture-handler": "^2.16.1", "react-native-reanimated": "^3.10.1", "react-native-safe-area-context": "^4.10.5", "react-native-screens": "4.11.0", "react-native-svg": "15.12.0", "react-native-web": "~0.19.6", "sonner-native": "0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "typescript": "^5.1.3"}}