import { Agent } from '@mastra/core';
import { z } from 'zod';
export declare const projectManagerAgent: Agent<"Project Management Agent", {
    createProjectPlan: import("@mastra/core/dist/base-QP4OC4dB").ad<z.ZodObject<{
        projectName: z.ZodString;
        endDate: z.ZodString;
        totalTasks: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    }, "strip", z.ZodTypeAny, {
        projectName: string;
        endDate: string;
        totalTasks: number;
    }, {
        projectName: string;
        endDate: string;
        totalTasks?: number | undefined;
    }>, undefined, import("@mastra/core").ToolExecutionContext<z.ZodObject<{
        projectName: z.ZodString;
        endDate: z.ZodString;
        totalTasks: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    }, "strip", z.<PERSON><PERSON><PERSON>ype<PERSON>, {
        projectName: string;
        endDate: string;
        totalTasks: number;
    }, {
        projectName: string;
        endDate: string;
        totalTasks?: number | undefined;
    }>>>;
}, Record<string, import("@mastra/core").Metric>>;
//# sourceMappingURL=assistant.d.ts.map