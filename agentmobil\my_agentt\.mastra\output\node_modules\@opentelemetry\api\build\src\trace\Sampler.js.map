{"version": 3, "file": "Sampler.js", "sourceRoot": "", "sources": ["../../../src/trace/Sampler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '../context/types';\nimport { SpanAttributes } from './attributes';\nimport { Link } from './link';\nimport { SamplingResult } from './SamplingResult';\nimport { SpanKind } from './span_kind';\n\n/**\n * @deprecated use the one declared in @opentelemetry/sdk-trace-base instead.\n * This interface represent a sampler. Sampling is a mechanism to control the\n * noise and overhead introduced by OpenTelemetry by reducing the number of\n * samples of traces collected and sent to the backend.\n */\nexport interface Sampler {\n  /**\n   * Checks whether span needs to be created and tracked.\n   *\n   * @param context Parent Context which may contain a span.\n   * @param traceId of the span to be created. It can be different from the\n   *     traceId in the {@link SpanContext}. Typically in situations when the\n   *     span to be created starts a new trace.\n   * @param spanName of the span to be created.\n   * @param spanKind of the span to be created.\n   * @param attributes Initial set of SpanAttributes for the Span being constructed.\n   * @param links Collection of links that will be associated with the Span to\n   *     be created. Typically useful for batch operations.\n   * @returns a {@link SamplingResult}.\n   */\n  shouldSample(\n    context: Context,\n    traceId: string,\n    spanName: string,\n    spanKind: SpanKind,\n    attributes: SpanAttributes,\n    links: Link[]\n  ): SamplingResult;\n\n  /** Returns the sampler name or short description with the configuration. */\n  toString(): string;\n}\n"]}