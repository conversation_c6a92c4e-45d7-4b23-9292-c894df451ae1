{"version": 3, "sources": ["forceTouchGesture.ts"], "names": ["ContinousBaseGesture", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "forceChange", "force", "ForceTouchGesture", "constructor", "handler<PERSON>ame", "minForce", "config", "max<PERSON><PERSON>ce", "feedbackOnActivation", "value", "onChange", "callback", "handlers"], "mappings": ";;AAAA,SAA4BA,oBAA5B,QAAwD,WAAxD;;AASA,SAASC,qBAAT,CACEC,OADF,EAEEC,QAFF,EAGE;AACA;;AACA,MAAIC,aAAJ;;AACA,MAAID,QAAQ,KAAKE,SAAjB,EAA4B;AAC1BD,IAAAA,aAAa,GAAG;AACdE,MAAAA,WAAW,EAAEJ,OAAO,CAACK;AADP,KAAhB;AAGD,GAJD,MAIO;AACLH,IAAAA,aAAa,GAAG;AACdE,MAAAA,WAAW,EAAEJ,OAAO,CAACK,KAAR,GAAgBJ,QAAQ,CAACI;AADxB,KAAhB;AAGD;;AAED,SAAO,EAAE,GAAGL,OAAL;AAAc,OAAGE;AAAjB,GAAP;AACD;;AAED,OAAO,MAAMI,iBAAN,SAAgCR,oBAAhC,CAGL;AAGAS,EAAAA,WAAW,GAAG;AACZ;;AADY,oCAF+C,EAE/C;;AAGZ,SAAKC,WAAL,GAAmB,0BAAnB;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,QAAQ,CAACJ,KAAD,EAAgB;AACtB,SAAKK,MAAL,CAAYD,QAAZ,GAAuBJ,KAAvB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEM,EAAAA,QAAQ,CAACN,KAAD,EAAgB;AACtB,SAAKK,MAAL,CAAYC,QAAZ,GAAuBN,KAAvB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEO,EAAAA,oBAAoB,CAACC,KAAD,EAAiB;AACnC,SAAKH,MAAL,CAAYE,oBAAZ,GAAmCC,KAAnC;AACA,WAAO,IAAP;AACD;;AAEDC,EAAAA,QAAQ,CACNC,QADM,EASN;AACA;AACA,SAAKC,QAAL,CAAcjB,qBAAd,GAAsCA,qBAAtC;AACA,WAAO,MAAMe,QAAN,CAAeC,QAAf,CAAP;AACD;;AAnDD", "sourcesContent": ["import { BaseGestureConfig, ContinousBaseGesture } from './gesture';\nimport { ForceTouchGestureConfig } from '../ForceTouchGestureHandler';\nimport type { ForceTouchGestureHandlerEventPayload } from '../GestureHandlerEventPayload';\nimport { GestureUpdateEvent } from '../gestureHandlerCommon';\n\nexport type ForceTouchGestureChangeEventPayload = {\n  forceChange: number;\n};\n\nfunction changeEventCalculator(\n  current: GestureUpdateEvent<ForceTouchGestureHandlerEventPayload>,\n  previous?: GestureUpdateEvent<ForceTouchGestureHandlerEventPayload>\n) {\n  'worklet';\n  let changePayload: ForceTouchGestureChangeEventPayload;\n  if (previous === undefined) {\n    changePayload = {\n      forceChange: current.force,\n    };\n  } else {\n    changePayload = {\n      forceChange: current.force - previous.force,\n    };\n  }\n\n  return { ...current, ...changePayload };\n}\n\nexport class ForceTouchGesture extends ContinousBaseGesture<\n  ForceTouchGestureHandlerEventPayload,\n  ForceTouchGestureChangeEventPayload\n> {\n  public config: BaseGestureConfig & ForceTouchGestureConfig = {};\n\n  constructor() {\n    super();\n\n    this.handlerName = 'ForceTouchGestureHandler';\n  }\n\n  /**\n   * A minimal pressure that is required before gesture can activate.\n   * Should be a value from range [0.0, 1.0]. Default is 0.2.\n   * @param force\n   */\n  minForce(force: number) {\n    this.config.minForce = force;\n    return this;\n  }\n\n  /**\n   * A maximal pressure that could be applied for gesture.\n   * If the pressure is greater, gesture fails. Should be a value from range [0.0, 1.0].\n   * @param force\n   */\n  maxForce(force: number) {\n    this.config.maxForce = force;\n    return this;\n  }\n\n  /**\n   * Value defining if haptic feedback has to be performed on activation.\n   * @param value\n   */\n  feedbackOnActivation(value: boolean) {\n    this.config.feedbackOnActivation = value;\n    return this;\n  }\n\n  onChange(\n    callback: (\n      event: GestureUpdateEvent<\n        GestureUpdateEvent<\n          ForceTouchGestureHandlerEventPayload &\n            ForceTouchGestureChangeEventPayload\n        >\n      >\n    ) => void\n  ) {\n    // @ts-ignore TS being overprotective, ForceTouchGestureHandlerEventPayload is Record\n    this.handlers.changeEventCalculator = changeEventCalculator;\n    return super.onChange(callback);\n  }\n}\n\nexport type ForceTouchGestureType = InstanceType<typeof ForceTouchGesture>;\n"]}