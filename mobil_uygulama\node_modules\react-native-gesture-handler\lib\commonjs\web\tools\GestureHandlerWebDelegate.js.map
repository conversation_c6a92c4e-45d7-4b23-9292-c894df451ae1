{"version": 3, "sources": ["GestureHandlerWebDelegate.ts"], "names": ["GestureHandlerWebDelegate", "userSelect", "touchAction", "get<PERSON>iew", "view", "init", "viewRef", "handler", "Error", "getTag", "isInitialized", "<PERSON><PERSON><PERSON><PERSON>", "defaultViewStyles", "style", "config", "getConfig", "setUserSelect", "enabled", "setTouchAction", "setContextMenu", "eventManagers", "push", "PointerEventManager", "KeyboardEventManager", "for<PERSON>ach", "manager", "attachEventManager", "isPointerInBounds", "x", "y", "measure<PERSON>iew", "rect", "getBoundingClientRect", "pageX", "left", "pageY", "top", "width", "height", "reset", "resetManager", "tryResetCursor", "activeCursor", "getState", "State", "ACTIVE", "cursor", "shouldDisableContextMenu", "enableContextMenu", "undefined", "isButtonInConfig", "MouseB<PERSON>on", "RIGHT", "addContextMenuListeners", "addEventListener", "disableContextMenu", "removeContextMenuListeners", "removeEventListener", "e", "preventDefault", "stopPropagation", "isHandlerEnabled", "onEnabledChange", "onBegin", "onActivate", "onEnd", "onCancel", "onFail", "destroy", "unregisterListeners"], "mappings": ";;;;;;;AAAA;;AAMA;;AACA;;AACA;;AAGA;;AACA;;;;;;AAOO,MAAMA,yBAAN,CAEP;AAAA;AAAA,2CAC0B,KAD1B;;AAAA;;AAAA;;AAAA,2CAImD,EAJnD;;AAAA,+CAKiD;AAC7CC,MAAAA,UAAU,EAAE,EADiC;AAE7CC,MAAAA,WAAW,EAAE;AAFgC,KALjD;AAAA;;AAUEC,EAAAA,OAAO,GAAgB;AACrB,WAAO,KAAKC,IAAZ;AACD;;AAEDC,EAAAA,IAAI,CAACC,OAAD,EAAkBC,OAAlB,EAAkD;AACpD,QAAI,CAACD,OAAL,EAAc;AACZ,YAAM,IAAIE,KAAJ,CACH,wCAAuCD,OAAO,CAACE,MAAR,EAAiB,EADrD,CAAN;AAGD;;AAED,SAAKC,aAAL,GAAqB,IAArB;AAEA,SAAKC,cAAL,GAAsBJ,OAAtB;AACA,SAAKH,IAAL,GAAY,iCAAeE,OAAf,CAAZ;AAEA,SAAKM,iBAAL,GAAyB;AACvBX,MAAAA,UAAU,EAAE,KAAKG,IAAL,CAAUS,KAAV,CAAgBZ,UADL;AAEvBC,MAAAA,WAAW,EAAE,KAAKE,IAAL,CAAUS,KAAV,CAAgBX;AAFN,KAAzB;AAKA,UAAMY,MAAM,GAAGP,OAAO,CAACQ,SAAR,EAAf;AAEA,SAAKC,aAAL,CAAmBF,MAAM,CAACG,OAA1B;AACA,SAAKC,cAAL,CAAoBJ,MAAM,CAACG,OAA3B;AACA,SAAKE,cAAL,CAAoBL,MAAM,CAACG,OAA3B;AAEA,SAAKG,aAAL,CAAmBC,IAAnB,CAAwB,IAAIC,4BAAJ,CAAwB,KAAKlB,IAA7B,CAAxB;AACA,SAAKgB,aAAL,CAAmBC,IAAnB,CAAwB,IAAIE,6BAAJ,CAAyB,KAAKnB,IAA9B,CAAxB;AAEA,SAAKgB,aAAL,CAAmBI,OAAnB,CAA4BC,OAAD,IACzB,KAAKd,cAAL,CAAoBe,kBAApB,CAAuCD,OAAvC,CADF;AAGD;;AAEDE,EAAAA,iBAAiB,CAAC;AAAEC,IAAAA,CAAF;AAAKC,IAAAA;AAAL,GAAD,EAA8C;AAC7D,WAAO,8BAAkB,KAAKzB,IAAvB,EAA6B;AAAEwB,MAAAA,CAAF;AAAKC,MAAAA;AAAL,KAA7B,CAAP;AACD;;AAEDC,EAAAA,WAAW,GAAkB;AAC3B,UAAMC,IAAI,GAAG,KAAK3B,IAAL,CAAU4B,qBAAV,EAAb;AAEA,WAAO;AACLC,MAAAA,KAAK,EAAEF,IAAI,CAACG,IADP;AAELC,MAAAA,KAAK,EAAEJ,IAAI,CAACK,GAFP;AAGLC,MAAAA,KAAK,EAAEN,IAAI,CAACM,KAHP;AAILC,MAAAA,MAAM,EAAEP,IAAI,CAACO;AAJR,KAAP;AAMD;;AAEDC,EAAAA,KAAK,GAAS;AACZ,SAAKnB,aAAL,CAAmBI,OAAnB,CAA4BC,OAAD,IACzBA,OAAO,CAACe,YAAR,EADF;AAGD;;AAEDC,EAAAA,cAAc,GAAG;AACf,UAAM3B,MAAM,GAAG,KAAKH,cAAL,CAAoBI,SAApB,EAAf;;AAEA,QACED,MAAM,CAAC4B,YAAP,IACA5B,MAAM,CAAC4B,YAAP,KAAwB,MADxB,IAEA,KAAK/B,cAAL,CAAoBgC,QAApB,OAAmCC,aAAMC,MAH3C,EAIE;AACA,WAAKzC,IAAL,CAAUS,KAAV,CAAgBiC,MAAhB,GAAyB,MAAzB;AACD;AACF;;AAEOC,EAAAA,wBAAwB,CAACjC,MAAD,EAAiB;AAC/C,WACGA,MAAM,CAACkC,iBAAP,KAA6BC,SAA7B,IACC,KAAKtC,cAAL,CAAoBuC,gBAApB,CAAqCC,kCAAYC,KAAjD,CADF,IAEAtC,MAAM,CAACkC,iBAAP,KAA6B,KAH/B;AAKD;;AAEOK,EAAAA,uBAAuB,CAACvC,MAAD,EAAuB;AACpD,QAAI,KAAKiC,wBAAL,CAA8BjC,MAA9B,CAAJ,EAA2C;AACzC,WAAKV,IAAL,CAAUkD,gBAAV,CAA2B,aAA3B,EAA0C,KAAKC,kBAA/C;AACD,KAFD,MAEO,IAAIzC,MAAM,CAACkC,iBAAX,EAA8B;AACnC,WAAK5C,IAAL,CAAUkD,gBAAV,CAA2B,aAA3B,EAA0C,KAAKN,iBAA/C;AACD;AACF;;AAEOQ,EAAAA,0BAA0B,CAAC1C,MAAD,EAAuB;AACvD,QAAI,KAAKiC,wBAAL,CAA8BjC,MAA9B,CAAJ,EAA2C;AACzC,WAAKV,IAAL,CAAUqD,mBAAV,CAA8B,aAA9B,EAA6C,KAAKF,kBAAlD;AACD,KAFD,MAEO,IAAIzC,MAAM,CAACkC,iBAAX,EAA8B;AACnC,WAAK5C,IAAL,CAAUqD,mBAAV,CAA8B,aAA9B,EAA6C,KAAKT,iBAAlD;AACD;AACF;;AAEOO,EAAAA,kBAAkB,CAAaG,CAAb,EAAkC;AAC1DA,IAAAA,CAAC,CAACC,cAAF;AACD;;AAEOX,EAAAA,iBAAiB,CAAaU,CAAb,EAAkC;AACzDA,IAAAA,CAAC,CAACE,eAAF;AACD;;AAEO5C,EAAAA,aAAa,CAAC6C,gBAAD,EAA4B;AAC/C,UAAM;AAAE5D,MAAAA;AAAF,QAAiB,KAAKU,cAAL,CAAoBI,SAApB,EAAvB;AAEA,SAAKX,IAAL,CAAUS,KAAV,CAAgB,YAAhB,IAAgCgD,gBAAgB,GAC5C5D,UAD4C,aAC5CA,UAD4C,cAC5CA,UAD4C,GAC9B,MAD8B,GAE5C,KAAKW,iBAAL,CAAuBX,UAF3B;AAIA,SAAKG,IAAL,CAAUS,KAAV,CAAgB,kBAAhB,IAAsCgD,gBAAgB,GAClD5D,UADkD,aAClDA,UADkD,cAClDA,UADkD,GACpC,MADoC,GAElD,KAAKW,iBAAL,CAAuBX,UAF3B;AAGD;;AAEOiB,EAAAA,cAAc,CAAC2C,gBAAD,EAA4B;AAChD,UAAM;AAAE3D,MAAAA;AAAF,QAAkB,KAAKS,cAAL,CAAoBI,SAApB,EAAxB;AAEA,SAAKX,IAAL,CAAUS,KAAV,CAAgB,aAAhB,IAAiCgD,gBAAgB,GAC7C3D,WAD6C,aAC7CA,WAD6C,cAC7CA,WAD6C,GAC9B,MAD8B,GAE7C,KAAKU,iBAAL,CAAuBV,WAF3B,CAHgD,CAOhD;;AACA,SAAKE,IAAL,CAAUS,KAAV,CAAgB,oBAAhB,IAAwCgD,gBAAgB,GACpD3D,WADoD,aACpDA,WADoD,cACpDA,WADoD,GACrC,MADqC,GAEpD,KAAKU,iBAAL,CAAuBV,WAF3B;AAGD;;AAEOiB,EAAAA,cAAc,CAAC0C,gBAAD,EAA4B;AAChD,UAAM/C,MAAM,GAAG,KAAKH,cAAL,CAAoBI,SAApB,EAAf;;AAEA,QAAI8C,gBAAJ,EAAsB;AACpB,WAAKR,uBAAL,CAA6BvC,MAA7B;AACD,KAFD,MAEO;AACL,WAAK0C,0BAAL,CAAgC1C,MAAhC;AACD;AACF;;AAEDgD,EAAAA,eAAe,CAAC7C,OAAD,EAAyB;AACtC,QAAI,CAAC,KAAKP,aAAV,EAAyB;AACvB;AACD;;AAED,SAAKM,aAAL,CAAmBC,OAAnB;AACA,SAAKC,cAAL,CAAoBD,OAApB;AACA,SAAKE,cAAL,CAAoBF,OAApB;AACD;;AAED8C,EAAAA,OAAO,GAAS,CACd;AACD;;AAEDC,EAAAA,UAAU,GAAS;AACjB,UAAMlD,MAAM,GAAG,KAAKH,cAAL,CAAoBI,SAApB,EAAf;;AAEA,QACE,CAAC,CAAC,KAAKX,IAAL,CAAUS,KAAV,CAAgBiC,MAAjB,IAA2B,KAAK1C,IAAL,CAAUS,KAAV,CAAgBiC,MAAhB,KAA2B,MAAvD,KACAhC,MAAM,CAAC4B,YAFT,EAGE;AACA,WAAKtC,IAAL,CAAUS,KAAV,CAAgBiC,MAAhB,GAAyBhC,MAAM,CAAC4B,YAAhC;AACD;AACF;;AAEDuB,EAAAA,KAAK,GAAS;AACZ,SAAKxB,cAAL;AACD;;AAEDyB,EAAAA,QAAQ,GAAS;AACf,SAAKzB,cAAL;AACD;;AAED0B,EAAAA,MAAM,GAAS;AACb,SAAK1B,cAAL;AACD;;AAEM2B,EAAAA,OAAO,CAACtD,MAAD,EAAuB;AACnC,SAAK0C,0BAAL,CAAgC1C,MAAhC;AAEA,SAAKM,aAAL,CAAmBI,OAAnB,CAA4BC,OAAD,IAAa;AACtCA,MAAAA,OAAO,CAAC4C,mBAAR;AACD,KAFD;AAGD;;AA5LH", "sourcesContent": ["import { findNodeHandle } from 'react-native';\nimport type IGestureHandler from '../handlers/IGestureHandler';\nimport {\n  GestureHandlerDelegate,\n  MeasureResult,\n} from './GestureHandlerDelegate';\nimport PointerEventManager from './PointerEventManager';\nimport { State } from '../../State';\nimport { isPointerInBounds } from '../utils';\nimport EventManager from './EventManager';\nimport { Config } from '../interfaces';\nimport { MouseButton } from '../../handlers/gestureHandlerCommon';\nimport KeyboardEventManager from './KeyboardEventManager';\n\ninterface DefaultViewStyles {\n  userSelect: string;\n  touchAction: string;\n}\n\nexport class GestureHandlerWebDelegate\n  implements GestureHandlerDelegate<HTMLElement, IGestureHandler>\n{\n  private isInitialized = false;\n  private view!: HTMLElement;\n  private gestureHandler!: IGestureHandler;\n  private eventManagers: EventManager<unknown>[] = [];\n  private defaultViewStyles: DefaultViewStyles = {\n    userSelect: '',\n    touchAction: '',\n  };\n\n  getView(): HTMLElement {\n    return this.view;\n  }\n\n  init(viewRef: number, handler: IGestureHandler): void {\n    if (!viewRef) {\n      throw new Error(\n        `Cannot find HTML Element for handler ${handler.getTag()}`\n      );\n    }\n\n    this.isInitialized = true;\n\n    this.gestureHandler = handler;\n    this.view = findNodeHandle(viewRef) as unknown as HTMLElement;\n\n    this.defaultViewStyles = {\n      userSelect: this.view.style.userSelect,\n      touchAction: this.view.style.touchAction,\n    };\n\n    const config = handler.getConfig();\n\n    this.setUserSelect(config.enabled);\n    this.setTouchAction(config.enabled);\n    this.setContextMenu(config.enabled);\n\n    this.eventManagers.push(new PointerEventManager(this.view));\n    this.eventManagers.push(new KeyboardEventManager(this.view));\n\n    this.eventManagers.forEach((manager) =>\n      this.gestureHandler.attachEventManager(manager)\n    );\n  }\n\n  isPointerInBounds({ x, y }: { x: number; y: number }): boolean {\n    return isPointerInBounds(this.view, { x, y });\n  }\n\n  measureView(): MeasureResult {\n    const rect = this.view.getBoundingClientRect();\n\n    return {\n      pageX: rect.left,\n      pageY: rect.top,\n      width: rect.width,\n      height: rect.height,\n    };\n  }\n\n  reset(): void {\n    this.eventManagers.forEach((manager: EventManager<unknown>) =>\n      manager.resetManager()\n    );\n  }\n\n  tryResetCursor() {\n    const config = this.gestureHandler.getConfig();\n\n    if (\n      config.activeCursor &&\n      config.activeCursor !== 'auto' &&\n      this.gestureHandler.getState() === State.ACTIVE\n    ) {\n      this.view.style.cursor = 'auto';\n    }\n  }\n\n  private shouldDisableContextMenu(config: Config) {\n    return (\n      (config.enableContextMenu === undefined &&\n        this.gestureHandler.isButtonInConfig(MouseButton.RIGHT)) ||\n      config.enableContextMenu === false\n    );\n  }\n\n  private addContextMenuListeners(config: Config): void {\n    if (this.shouldDisableContextMenu(config)) {\n      this.view.addEventListener('contextmenu', this.disableContextMenu);\n    } else if (config.enableContextMenu) {\n      this.view.addEventListener('contextmenu', this.enableContextMenu);\n    }\n  }\n\n  private removeContextMenuListeners(config: Config): void {\n    if (this.shouldDisableContextMenu(config)) {\n      this.view.removeEventListener('contextmenu', this.disableContextMenu);\n    } else if (config.enableContextMenu) {\n      this.view.removeEventListener('contextmenu', this.enableContextMenu);\n    }\n  }\n\n  private disableContextMenu(this: void, e: MouseEvent): void {\n    e.preventDefault();\n  }\n\n  private enableContextMenu(this: void, e: MouseEvent): void {\n    e.stopPropagation();\n  }\n\n  private setUserSelect(isHandlerEnabled: boolean) {\n    const { userSelect } = this.gestureHandler.getConfig();\n\n    this.view.style['userSelect'] = isHandlerEnabled\n      ? userSelect ?? 'none'\n      : this.defaultViewStyles.userSelect;\n\n    this.view.style['webkitUserSelect'] = isHandlerEnabled\n      ? userSelect ?? 'none'\n      : this.defaultViewStyles.userSelect;\n  }\n\n  private setTouchAction(isHandlerEnabled: boolean) {\n    const { touchAction } = this.gestureHandler.getConfig();\n\n    this.view.style['touchAction'] = isHandlerEnabled\n      ? touchAction ?? 'none'\n      : this.defaultViewStyles.touchAction;\n\n    // @ts-ignore This one disables default events on Safari\n    this.view.style['WebkitTouchCallout'] = isHandlerEnabled\n      ? touchAction ?? 'none'\n      : this.defaultViewStyles.touchAction;\n  }\n\n  private setContextMenu(isHandlerEnabled: boolean) {\n    const config = this.gestureHandler.getConfig();\n\n    if (isHandlerEnabled) {\n      this.addContextMenuListeners(config);\n    } else {\n      this.removeContextMenuListeners(config);\n    }\n  }\n\n  onEnabledChange(enabled: boolean): void {\n    if (!this.isInitialized) {\n      return;\n    }\n\n    this.setUserSelect(enabled);\n    this.setTouchAction(enabled);\n    this.setContextMenu(enabled);\n  }\n\n  onBegin(): void {\n    // no-op for now\n  }\n\n  onActivate(): void {\n    const config = this.gestureHandler.getConfig();\n\n    if (\n      (!this.view.style.cursor || this.view.style.cursor === 'auto') &&\n      config.activeCursor\n    ) {\n      this.view.style.cursor = config.activeCursor;\n    }\n  }\n\n  onEnd(): void {\n    this.tryResetCursor();\n  }\n\n  onCancel(): void {\n    this.tryResetCursor();\n  }\n\n  onFail(): void {\n    this.tryResetCursor();\n  }\n\n  public destroy(config: Config): void {\n    this.removeContextMenuListeners(config);\n\n    this.eventManagers.forEach((manager) => {\n      manager.unregisterListeners();\n    });\n  }\n}\n"]}