{"version": 3, "sources": ["TouchableHighlight.tsx"], "names": ["React", "Component", "GenericTouchable", "TOUCHABLE_STATE", "StyleSheet", "View", "TouchableHighlight", "constructor", "props", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setState", "extraChildStyle", "opacity", "activeOpacity", "extraUnderlayStyle", "backgroundColor", "underlayColor", "onShowUnderlay", "onPress", "onPressIn", "onPressOut", "onLongPress", "onHideUnderlay", "_from", "to", "BEGAN", "showUnderlay", "UNDETERMINED", "MOVED_OUTSIDE", "<PERSON><PERSON><PERSON><PERSON>", "state", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "child", "Children", "only", "cloneElement", "style", "compose", "render", "rest", "onStateChange", "defaultProps", "delayPressOut"], "mappings": ";;;;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AACA,SAASC,SAAT,QAA0B,OAA1B;AACA,OAAOC,gBAAP,IAA2BC,eAA3B,QAAkD,oBAAlD;AAEA,SACEC,UADF,EAEEC,IAFF,QAMO,cANP;;AAoBA;AACA;AACA;AACA,eAAe,MAAMC,kBAAN,SAAiCL,SAAjC,CAGb;AAQAM,EAAAA,WAAW,CAACC,KAAD,EAAiC;AAC1C,UAAMA,KAAN;;AAD0C,0CAS7B,MAAM;AAAA;;AACnB,UAAI,CAAC,KAAKC,eAAL,EAAL,EAA6B;AAC3B;AACD;;AACD,WAAKC,QAAL,CAAc;AACZC,QAAAA,eAAe,EAAE;AACfC,UAAAA,OAAO,EAAE,KAAKJ,KAAL,CAAWK;AADL,SADL;AAIZC,QAAAA,kBAAkB,EAAE;AAClBC,UAAAA,eAAe,EAAE,KAAKP,KAAL,CAAWQ;AADV;AAJR,OAAd;AAQA,mDAAKR,KAAL,EAAWS,cAAX;AACD,KAtB2C;;AAAA,6CAwB1B,MAChB,KAAKT,KAAL,CAAWU,OAAX,IACA,KAAKV,KAAL,CAAWW,SADX,IAEA,KAAKX,KAAL,CAAWY,UAFX,IAGA,KAAKZ,KAAL,CAAWa,WA5B+B;;AAAA,0CA8B7B,MAAM;AAAA;;AACnB,WAAKX,QAAL,CAAc;AACZC,QAAAA,eAAe,EAAE,IADL;AAEZG,QAAAA,kBAAkB,EAAE;AAFR,OAAd;AAIA,oDAAKN,KAAL,EAAWc,cAAX;AACD,KApC2C;;AAAA,2CAmD5B,CAACC,KAAD,EAAgBC,EAAhB,KAA+B;AAC7C,UAAIA,EAAE,KAAKrB,eAAe,CAACsB,KAA3B,EAAkC;AAChC,aAAKC,YAAL;AACD,OAFD,MAEO,IACLF,EAAE,KAAKrB,eAAe,CAACwB,YAAvB,IACAH,EAAE,KAAKrB,eAAe,CAACyB,aAFlB,EAGL;AACA,aAAKC,YAAL;AACD;AACF,KA5D2C;;AAE1C,SAAKC,KAAL,GAAa;AACXnB,MAAAA,eAAe,EAAE,IADN;AAEXG,MAAAA,kBAAkB,EAAE;AAFT,KAAb;AAID,GAdD,CAgBA;;;AA8BAiB,EAAAA,cAAc,GAAG;AACf,QAAI,CAAC,KAAKvB,KAAL,CAAWwB,QAAhB,EAA0B;AACxB,0BAAO,oBAAC,IAAD,OAAP;AACD;;AAED,UAAMC,KAAK,GAAGjC,KAAK,CAACkC,QAAN,CAAeC,IAAf,CACZ,KAAK3B,KAAL,CAAWwB,QADC,CAAd,CALe,CAOqB;;AACpC,wBAAOhC,KAAK,CAACoC,YAAN,CAAmBH,KAAnB,EAA0B;AAC/BI,MAAAA,KAAK,EAAEjC,UAAU,CAACkC,OAAX,CAAmBL,KAAK,CAACzB,KAAN,CAAY6B,KAA/B,EAAsC,KAAKP,KAAL,CAAWnB,eAAjD;AADwB,KAA1B,CAAP;AAGD;;AAaD4B,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEF,MAAAA,KAAK,GAAG,EAAV;AAAc,SAAGG;AAAjB,QAA0B,KAAKhC,KAArC;AACA,UAAM;AAAEM,MAAAA;AAAF,QAAyB,KAAKgB,KAApC;AACA,wBACE,oBAAC,gBAAD,eACMU,IADN;AAEE,MAAA,KAAK,EAAE,CAACH,KAAD,EAAQvB,kBAAR,CAFT;AAGE,MAAA,aAAa,EAAE,KAAK2B;AAHtB,QAIG,KAAKV,cAAL,EAJH,CADF;AAQD;;AAjFD;;gBAHmBzB,kB,kBAIG,EACpB,GAAGJ,gBAAgB,CAACwC,YADA;AAEpB7B,EAAAA,aAAa,EAAE,IAFK;AAGpB8B,EAAAA,aAAa,EAAE,GAHK;AAIpB3B,EAAAA,aAAa,EAAE;AAJK,C", "sourcesContent": ["import * as React from 'react';\nimport { Component } from 'react';\nimport GenericTouchable, { TOUCHABLE_STATE } from './GenericTouchable';\nimport type { GenericTouchableProps } from './GenericTouchableProps';\nimport {\n  StyleSheet,\n  View,\n  TouchableHighlightProps as RNTouchableHighlightProps,\n  ColorValue,\n  ViewProps,\n} from 'react-native';\n\ninterface State {\n  extraChildStyle: null | {\n    opacity?: number;\n  };\n  extraUnderlayStyle: null | {\n    backgroundColor?: ColorValue;\n  };\n}\n\nexport type TouchableHighlightProps = RNTouchableHighlightProps &\n  GenericTouchableProps;\n\n/**\n * TouchableHighlight follows RN's implementation\n */\nexport default class TouchableHighlight extends Component<\n  TouchableHighlightProps,\n  State\n> {\n  static defaultProps = {\n    ...GenericTouchable.defaultProps,\n    activeOpacity: 0.85,\n    delayPressOut: 100,\n    underlayColor: 'black',\n  };\n\n  constructor(props: TouchableHighlightProps) {\n    super(props);\n    this.state = {\n      extraChildStyle: null,\n      extraUnderlayStyle: null,\n    };\n  }\n\n  // Copied from RN\n  showUnderlay = () => {\n    if (!this.hasPressHandler()) {\n      return;\n    }\n    this.setState({\n      extraChildStyle: {\n        opacity: this.props.activeOpacity,\n      },\n      extraUnderlayStyle: {\n        backgroundColor: this.props.underlayColor,\n      },\n    });\n    this.props.onShowUnderlay?.();\n  };\n\n  hasPressHandler = () =>\n    this.props.onPress ||\n    this.props.onPressIn ||\n    this.props.onPressOut ||\n    this.props.onLongPress;\n\n  hideUnderlay = () => {\n    this.setState({\n      extraChildStyle: null,\n      extraUnderlayStyle: null,\n    });\n    this.props.onHideUnderlay?.();\n  };\n\n  renderChildren() {\n    if (!this.props.children) {\n      return <View />;\n    }\n\n    const child = React.Children.only(\n      this.props.children\n    ) as React.ReactElement<ViewProps>; // TODO: not sure if OK but fixes error\n    return React.cloneElement(child, {\n      style: StyleSheet.compose(child.props.style, this.state.extraChildStyle),\n    });\n  }\n\n  onStateChange = (_from: number, to: number) => {\n    if (to === TOUCHABLE_STATE.BEGAN) {\n      this.showUnderlay();\n    } else if (\n      to === TOUCHABLE_STATE.UNDETERMINED ||\n      to === TOUCHABLE_STATE.MOVED_OUTSIDE\n    ) {\n      this.hideUnderlay();\n    }\n  };\n\n  render() {\n    const { style = {}, ...rest } = this.props;\n    const { extraUnderlayStyle } = this.state;\n    return (\n      <GenericTouchable\n        {...rest}\n        style={[style, extraUnderlayStyle]}\n        onStateChange={this.onStateChange}>\n        {this.renderChildren()}\n      </GenericTouchable>\n    );\n  }\n}\n"]}