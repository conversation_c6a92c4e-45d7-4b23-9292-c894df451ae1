{"version": 3, "file": "autoLoaderUtils.js", "sourceRoot": "", "sources": ["../../src/autoLoaderUtils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAMH;;;;;GAKG;AACH,MAAM,UAAU,sBAAsB,CACpC,gBAAmC,EACnC,cAA+B,EAC/B,aAA6B,EAC7B,cAA+B;IAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACvD,MAAM,eAAe,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,cAAc,EAAE;YAClB,eAAe,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;SACnD;QACD,IAAI,aAAa,EAAE;YACjB,eAAe,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;SACjD;QACD,IAAI,cAAc,IAAI,eAAe,CAAC,iBAAiB,EAAE;YACvD,eAAe,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;SACnD;QACD,6DAA6D;QAC7D,oEAAoE;QACpE,mEAAmE;QACnE,yCAAyC;QACzC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE;YACxC,eAAe,CAAC,MAAM,EAAE,CAAC;SAC1B;KACF;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,uBAAuB,CACrC,gBAAmC;IAEnC,gBAAgB,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;AACzE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { TracerProvider, MeterProvider } from '@opentelemetry/api';\nimport { Instrumentation } from './types';\nimport { LoggerProvider } from '@opentelemetry/api-logs';\n\n/**\n * Enable instrumentations\n * @param instrumentations\n * @param tracerProvider\n * @param meterProvider\n */\nexport function enableInstrumentations(\n  instrumentations: Instrumentation[],\n  tracerProvider?: TracerProvider,\n  meterProvider?: MeterProvider,\n  loggerProvider?: LoggerProvider\n): void {\n  for (let i = 0, j = instrumentations.length; i < j; i++) {\n    const instrumentation = instrumentations[i];\n    if (tracerProvider) {\n      instrumentation.setTracerProvider(tracerProvider);\n    }\n    if (meterProvider) {\n      instrumentation.setMeterProvider(meterProvider);\n    }\n    if (loggerProvider && instrumentation.setLoggerProvider) {\n      instrumentation.setLoggerProvider(loggerProvider);\n    }\n    // instrumentations have been already enabled during creation\n    // so enable only if user prevented that by setting enabled to false\n    // this is to prevent double enabling but when calling register all\n    // instrumentations should be now enabled\n    if (!instrumentation.getConfig().enabled) {\n      instrumentation.enable();\n    }\n  }\n}\n\n/**\n * Disable instrumentations\n * @param instrumentations\n */\nexport function disableInstrumentations(\n  instrumentations: Instrumentation[]\n): void {\n  instrumentations.forEach(instrumentation => instrumentation.disable());\n}\n"]}