{"version": 3, "sources": ["useViewRefHandler.ts"], "names": ["isF<PERSON><PERSON>", "tagMessage", "getShadowNodeFromRef", "useCallback", "findNodeHandle", "useViewRefHandler", "state", "updateAttachedGestures", "ref<PERSON><PERSON><PERSON>", "ref", "viewRef", "previousViewTag", "firstRender", "__DEV__", "global", "isFormsStackingContext", "node", "console", "error"], "mappings": "AAAA,SAASA,QAAT,EAAmBC,UAAnB,QAAqC,gBAArC;AACA,SAASC,oBAAT,QAAqC,+BAArC;AAGA,SAAgBC,WAAhB,QAAmC,OAAnC;AACA,SAASC,cAAT,QAA+B,cAA/B;AAMA;AACA;AACA;AACA,OAAO,SAASC,iBAAT,CACLC,KADK,EAELC,sBAFK,EAGL;AACA,QAAMC,UAAU,GAAGL,WAAW,CAC3BM,GAAD,IAAiC;AAC/B,QAAIA,GAAG,KAAK,IAAZ,EAAkB;AAChB;AACD;;AAEDH,IAAAA,KAAK,CAACI,OAAN,GAAgBD,GAAhB,CAL+B,CAO/B;;AACA,QAAIH,KAAK,CAACK,eAAN,KAA0B,CAAC,CAA/B,EAAkC;AAChCL,MAAAA,KAAK,CAACK,eAAN,GAAwBP,cAAc,CAACE,KAAK,CAACI,OAAP,CAAtC;AACD,KAV8B,CAY/B;AACA;;;AACA,QAAI,CAACJ,KAAK,CAACM,WAAX,EAAwB;AACtBL,MAAAA,sBAAsB,CAAC,IAAD,CAAtB;AACD;;AAED,QAAIM,OAAO,IAAIb,QAAQ,EAAnB,IAAyBc,MAAM,CAACC,sBAApC,EAA4D;AAC1D,YAAMC,IAAI,GAAGd,oBAAoB,CAACO,GAAD,CAAjC;;AACA,UAAIK,MAAM,CAACC,sBAAP,CAA8BC,IAA9B,MAAwC,KAA5C,EAAmD;AACjDC,QAAAA,OAAO,CAACC,KAAR,CACEjB,UAAU,CACR,uEACE,kGAFM,CADZ;AAMD;AACF;AACF,GA9B2B,EA+B5B,CAACK,KAAD,EAAQC,sBAAR,CA/B4B,CAA9B;AAkCA,SAAOC,UAAP;AACD", "sourcesContent": ["import { isFabric, tagMessage } from '../../../utils';\nimport { getShadowNodeFromRef } from '../../../getShadowNodeFromRef';\n\nimport { GestureDetectorState } from './types';\nimport React, { useCallback } from 'react';\nimport { findNodeHandle } from 'react-native';\n\ndeclare const global: {\n  isFormsStackingContext: (node: unknown) => boolean | null; // JSI function\n};\n\n// Ref handler for the Wrap component attached under the GestureDetector.\n// It's responsible for setting the viewRef on the state and triggering the reattaching of handlers\n// if the view has changed.\nexport function useViewRefHandler(\n  state: GestureDetectorState,\n  updateAttachedGestures: (skipConfigUpdate?: boolean) => void\n) {\n  const refHandler = useCallback(\n    (ref: React.Component | null) => {\n      if (ref === null) {\n        return;\n      }\n\n      state.viewRef = ref;\n\n      // if it's the first render, also set the previousViewTag to prevent reattaching gestures when not needed\n      if (state.previousViewTag === -1) {\n        state.previousViewTag = findNodeHandle(state.viewRef) as number;\n      }\n\n      // Pass true as `skipConfigUpdate`. Here we only want to trigger the eventual reattaching of handlers\n      // in case the view has changed. If the view doesn't change, the update will be handled by detector.\n      if (!state.firstRender) {\n        updateAttachedGestures(true);\n      }\n\n      if (__DEV__ && isFabric() && global.isFormsStackingContext) {\n        const node = getShadowNodeFromRef(ref);\n        if (global.isFormsStackingContext(node) === false) {\n          console.error(\n            tagMessage(\n              'GestureDetector has received a child that may get view-flattened. ' +\n                '\\nTo prevent it from misbehaving you need to wrap the child with a `<View collapsable={false}>`.'\n            )\n          );\n        }\n      }\n    },\n    [state, updateAttachedGestures]\n  );\n\n  return refHandler;\n}\n"]}