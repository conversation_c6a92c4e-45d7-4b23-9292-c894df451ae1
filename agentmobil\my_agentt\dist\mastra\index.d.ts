import { <PERSON><PERSON> } from '@mastra/core';
export declare const mastra: Ma<PERSON><{
    projectManager: import("@mastra/core").Agent<"Project Management Agent", {
        createProjectPlan: import("@mastra/core/dist/base-QP4OC4dB.js").ad<import("zod").ZodObject<{
            projectName: import("zod").ZodString;
            endDate: import("zod").ZodString;
            totalTasks: import("zod").ZodDefault<import("zod").ZodOptional<import("zod").ZodNumber>>;
        }, "strip", import("zod").ZodTypeAny, {
            projectName: string;
            endDate: string;
            totalTasks: number;
        }, {
            projectName: string;
            endDate: string;
            totalTasks?: number | undefined;
        }>, undefined, import("@mastra/core").ToolExecutionContext<import("zod").ZodObject<{
            projectName: import("zod").ZodString;
            endDate: import("zod").ZodString;
            totalTasks: import("zod").ZodDefault<import("zod").ZodOptional<import("zod").ZodNumber>>;
        }, "strip", import("zod").ZodTypeAny, {
            projectName: string;
            endDate: string;
            totalTasks: number;
        }, {
            projectName: string;
            endDate: string;
            totalTasks?: number | undefined;
        }>>>;
    }, Record<string, import("@mastra/core").Metric>>;
}, Record<string, import("@mastra/core/dist/base-QP4OC4dB.js").q<import("@mastra/core/dist/base-QP4OC4dB.js").p<string, any, any, import("@mastra/core/dist/base-QP4OC4dB.js").x<any, import("@mastra/core/dist/base-QP4OC4dB.js").s<any, import("@mastra/core/dist/base-QP4OC4dB.js").p<string, any, any, any>[], Record<string, any>>>>[], string, any, any>>, Record<string, import("@mastra/core/dist/base-QP4OC4dB.js").af<import("@mastra/core").Step<string, any, any, any, any>[], string, import("zod").ZodType<any, import("zod").ZodTypeDef, any>, import("zod").ZodType<any, import("zod").ZodTypeDef, any>, import("zod").ZodType<any, import("zod").ZodTypeDef, any>>>, Record<string, import("@mastra/core/dist/vector/index.js").MastraVector>, Record<string, import("@mastra/core/dist/tts/index.js").MastraTTS>, import("@mastra/core/dist/logger-EhZkzZOr.js").I, Record<string, import("@mastra/core/dist/base-QP4OC4dB.js").bh>, Record<string, import("@mastra/core/dist/base-QP4OC4dB.js").bi>>;
//# sourceMappingURL=index.d.ts.map