import { InstrumentationModuleDefinition, InstrumentationModuleFile } from './types';
export declare class InstrumentationNodeModuleDefinition implements InstrumentationModuleDefinition {
    name: string;
    supportedVersions: string[];
    patch?: ((exports: any, moduleVersion?: string) => any) | undefined;
    unpatch?: ((exports: any, moduleVersion?: string) => void) | undefined;
    files: InstrumentationModuleFile[];
    constructor(name: string, supportedVersions: string[], patch?: ((exports: any, moduleVersion?: string) => any) | undefined, unpatch?: ((exports: any, moduleVersion?: string) => void) | undefined, files?: InstrumentationModuleFile[]);
}
//# sourceMappingURL=instrumentationNodeModuleDefinition.d.ts.map