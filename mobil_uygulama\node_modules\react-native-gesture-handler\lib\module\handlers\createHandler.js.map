{"version": 3, "sources": ["createHandler.tsx"], "names": ["React", "Platform", "UIManager", "DeviceEventEmitter", "customDirectEventTypes", "RNGestureHandlerModule", "State", "handlerIDToTag", "registerOldGestureHandler", "getNextHandlerTag", "filterConfig", "findNodeHandle", "scheduleFlushOperations", "deepEqual", "isF<PERSON><PERSON>", "isJestEnv", "tagMessage", "ActionType", "PressabilityDebugView", "GestureHandlerRootViewContext", "ghQueueMicrotask", "UIManagerAny", "topGestureHandlerEvent", "registrationName", "customGHEventsConfigFabricAndroid", "topOnGestureHandlerEvent", "topOnGestureHandlerStateChange", "customGHEventsConfig", "onGestureHandlerEvent", "onGestureHandlerStateChange", "OS", "genericDirectEventTypes", "UIManagerConstants", "getViewManagerConfig", "getConstants", "setJSResponder", "oldSetJSResponder", "clearJSResponder", "oldClearJSResponder", "tag", "blockNativeResponder", "handleSetJSResponder", "handleClearJSResponder", "allowTouches", "DEV_ON_ANDROID", "__DEV__", "addListener", "hasUnresolvedRefs", "props", "extract", "refs", "Array", "isArray", "current", "some", "r", "stateToPropMappings", "UNDETERMINED", "undefined", "BEGAN", "FAILED", "CANCELLED", "ACTIVE", "END", "UNRESOLVED_REFS_RETRY_LIMIT", "createHandler", "name", "allowedProps", "config", "transformProps", "customNativeProps", "Handler", "Component", "constructor", "event", "nativeEvent", "handlerTag", "onGestureEvent", "onHandlerStateChange", "state", "stateEventName", "<PERSON><PERSON><PERSON><PERSON>", "node", "viewNode", "child", "Children", "only", "children", "ref", "newConfig", "createGestureHandler", "newViewTag", "viewTag", "attachGestureHandler", "JS_FUNCTION_OLD_API", "propsRef", "onGestureStateChange", "actionType", "isGestureHandlerWorklet", "isStateChangeHandlerWorklet", "is<PERSON><PERSON><PERSON>", "REANIMATED_WORKLET", "NATIVE_ANIMATED_EVENT", "updateGestureHandler", "createRef", "isMountedRef", "id", "Error", "componentDidMount", "inspectorToggleListener", "setState", "_", "update", "componentDidUpdate", "componentWillUnmount", "remove", "dropGestureHandler", "handlerID", "remainingTries", "setNativeProps", "updates", "mergedProps", "render", "context", "gestureEventHandler", "gestureStateEventHandler", "events", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "displayName", "toArray", "push", "hitSlop", "cloneElement", "ref<PERSON><PERSON><PERSON>", "collapsable", "handlerType", "enabled", "testID"], "mappings": ";;;;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AACA,SACEC,QADF,EAEEC,SAFF,EAGEC,kBAHF,QAKO,cALP;AAMA,SAASC,sBAAT,QAAuC,0BAAvC;AACA,OAAOC,sBAAP,MAAmC,2BAAnC;AACA,SAASC,KAAT,QAAsB,UAAtB;AACA,SAASC,cAAT,EAAyBC,yBAAzB,QAA0D,oBAA1D;AACA,SAASC,iBAAT,QAAkC,qBAAlC;AAOA,SAASC,YAAT,EAAuBC,cAAvB,EAAuCC,uBAAvC,QAAsE,SAAtE;AAEA,SAASC,SAAT,EAAoBC,QAApB,EAA8BC,SAA9B,EAAyCC,UAAzC,QAA2D,UAA3D;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,qBAAT,QAAsC,yBAAtC;AACA,OAAOC,6BAAP,MAA0C,kCAA1C;AACA,SAASC,gBAAT,QAAiC,qBAAjC;AAEA,MAAMC,YAAY,GAAGnB,SAArB;AAEAE,sBAAsB,CAACkB,sBAAvB,GAAgD;AAC9CC,EAAAA,gBAAgB,EAAE;AAD4B,CAAhD;AAIA,MAAMC,iCAAiC,GAAG;AACxCC,EAAAA,wBAAwB,EAAE;AAAEF,IAAAA,gBAAgB,EAAE;AAApB,GADc;AAExCG,EAAAA,8BAA8B,EAAE;AAC9BH,IAAAA,gBAAgB,EAAE;AADY;AAFQ,CAA1C;AAOA,MAAMI,oBAAoB,GAAG;AAC3BC,EAAAA,qBAAqB,EAAE;AAAEL,IAAAA,gBAAgB,EAAE;AAApB,GADI;AAE3BM,EAAAA,2BAA2B,EAAE;AAC3BN,IAAAA,gBAAgB,EAAE;AADS,GAFF;AAM3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAIT,QAAQ,MACVb,QAAQ,CAAC6B,EAAT,KAAgB,SADd,IAEFN,iCAFF;AAd2B,CAA7B,C,CAmBA;AACA;AACA;AACA;;AACAH,YAAY,CAACU,uBAAb,GAAuC,EACrC,GAAGV,YAAY,CAACU,uBADqB;AAErC,KAAGJ;AAFkC,CAAvC,C,CAIA;AACA;AACA;;AACA,MAAMK,kBAAkB,sDACtBX,YAAY,CAACY,oBADS,2DACtB,4BAAAZ,YAAY,EAAwB,cAAxB,CADU,kGAEtBA,YAAY,CAACa,YAFS,0DAEtB,2BAAAb,YAAY,CAFd;;AAIA,IAAIW,kBAAJ,EAAwB;AACtBA,EAAAA,kBAAkB,CAACD,uBAAnB,GAA6C,EAC3C,GAAGC,kBAAkB,CAACD,uBADqB;AAE3C,OAAGJ;AAFwC,GAA7C;AAID,C,CAED;;;AACA,MAAM;AACJQ,EAAAA,cAAc,EAAEC,iBAAiB,GAAG,MAAM,CACxC;AACD,GAHG;AAIJC,EAAAA,gBAAgB,EAAEC,mBAAmB,GAAG,MAAM,CAC5C;AACD;AANG,IAOFjB,YAPJ;;AAQAA,YAAY,CAACc,cAAb,GAA8B,CAACI,GAAD,EAAcC,oBAAd,KAAgD;AAC5EnC,EAAAA,sBAAsB,CAACoC,oBAAvB,CAA4CF,GAA5C,EAAiDC,oBAAjD;AACAJ,EAAAA,iBAAiB,CAACG,GAAD,EAAMC,oBAAN,CAAjB;AACD,CAHD;;AAIAnB,YAAY,CAACgB,gBAAb,GAAgC,MAAM;AACpChC,EAAAA,sBAAsB,CAACqC,sBAAvB;AACAJ,EAAAA,mBAAmB;AACpB,CAHD;;AAKA,IAAIK,YAAY,GAAG,IAAnB;AACA,MAAMC,cAAc,GAAGC,OAAO,IAAI5C,QAAQ,CAAC6B,EAAT,KAAgB,SAAlD,C,CACA;AACA;;AACA,IAAIc,cAAJ,EAAoB;AAClBzC,EAAAA,kBAAkB,CAAC2C,WAAnB,CAA+B,wBAA/B,EAAyD,MAAM;AAC7DH,IAAAA,YAAY,GAAG,CAACA,YAAhB;AACD,GAFD;AAGD;;AAKD,SAASI,iBAAT,CACEC,KADF,EAEE;AACA;AACA,QAAMC,OAAO,GAAIC,IAAD,IAAuB;AACrC,QAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,IAAd,CAAL,EAA0B;AACxB,aAAOA,IAAI,IAAIA,IAAI,CAACG,OAAL,KAAiB,IAAhC;AACD;;AACD,WAAOH,IAAI,CAACI,IAAL,CAAWC,CAAD,IAAOA,CAAC,IAAIA,CAAC,CAACF,OAAF,KAAc,IAApC,CAAP;AACD,GALD;;AAMA,SAAOJ,OAAO,CAACD,KAAK,CAAC,sBAAD,CAAN,CAAP,IAA0CC,OAAO,CAACD,KAAK,CAAC,SAAD,CAAN,CAAxD;AACD;;AAED,MAAMQ,mBAAmB,GAAG;AAC1B,GAAClD,KAAK,CAACmD,YAAP,GAAsBC,SADI;AAE1B,GAACpD,KAAK,CAACqD,KAAP,GAAe,SAFW;AAG1B,GAACrD,KAAK,CAACsD,MAAP,GAAgB,UAHU;AAI1B,GAACtD,KAAK,CAACuD,SAAP,GAAmB,aAJO;AAK1B,GAACvD,KAAK,CAACwD,MAAP,GAAgB,aALU;AAM1B,GAACxD,KAAK,CAACyD,GAAP,GAAa;AANa,CAA5B;AAgCA,MAAMC,2BAA2B,GAAG,CAApC,C,CAEA;;AACA,eAAe,SAASC,aAAT,CAGb;AACAC,EAAAA,IADA;AAEAC,EAAAA,YAAY,GAAG,EAFf;AAGAC,EAAAA,MAAM,GAAG,EAHT;AAIAC,EAAAA,cAJA;AAKAC,EAAAA,iBAAiB,GAAG;AALpB,CAHa,EAS6D;AAI1E,QAAMC,OAAN,SAAsBvE,KAAK,CAACwE,SAA5B,CAGE;AAYAC,IAAAA,WAAW,CAACzB,KAAD,EAAmC;AAC5C,YAAMA,KAAN;;AAD4C;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,qDAwEb0B,KAAD,IAA4B;AAC1D,YAAIA,KAAK,CAACC,WAAN,CAAkBC,UAAlB,KAAiC,KAAKA,UAA1C,EAAsD;AACpD,cAAI,OAAO,KAAK5B,KAAL,CAAW6B,cAAlB,KAAqC,UAAzC,EAAqD;AAAA;;AACnD,yDAAK7B,KAAL,EAAW6B,cAAX,kGAA4BH,KAA5B;AACD;AACF,SAJD,MAIO;AAAA;;AACL,yDAAK1B,KAAL,EAAWpB,qBAAX,qGAAmC8C,KAAnC;AACD;AACF,OAhF6C;;AAAA,2DAoF5CA,KADoC,IAEjC;AACH,YAAIA,KAAK,CAACC,WAAN,CAAkBC,UAAlB,KAAiC,KAAKA,UAA1C,EAAsD;AACpD,cAAI,OAAO,KAAK5B,KAAL,CAAW8B,oBAAlB,KAA2C,UAA/C,EAA2D;AAAA;;AACzD,0DAAK9B,KAAL,EAAW8B,oBAAX,mGAAkCJ,KAAlC;AACD;;AAED,gBAAMK,KAA4B,GAAGL,KAAK,CAACC,WAAN,CAAkBI,KAAvD;AACA,gBAAMC,cAAc,GAAGxB,mBAAmB,CAACuB,KAAD,CAA1C;AACA,gBAAME,YAAY,GAAGD,cAAc,IAAI,KAAKhC,KAAL,CAAWgC,cAAX,CAAvC;;AACA,cAAIC,YAAY,IAAI,OAAOA,YAAP,KAAwB,UAA5C,EAAwD;AACtDA,YAAAA,YAAY,CAACP,KAAD,CAAZ;AACD;AACF,SAXD,MAWO;AAAA;;AACL,yDAAK1B,KAAL,EAAWnB,2BAAX,qGAAyC6C,KAAzC;AACD;AACF,OApG6C;;AAAA,0CAsGxBQ,IAAD,IAAe;AAClC,aAAKC,QAAL,GAAgBD,IAAhB;AAEA,cAAME,KAAK,GAAGpF,KAAK,CAACqF,QAAN,CAAeC,IAAf,CAAoB,KAAKtC,KAAL,CAAWuC,QAA/B,CAAd,CAHkC,CAIlC;;AACA,cAAM;AAAEC,UAAAA;AAAF,YAAeJ,KAArB;;AACA,YAAII,GAAG,KAAK,IAAZ,EAAkB;AAChB,cAAI,OAAOA,GAAP,KAAe,UAAnB,EAA+B;AAC7BA,YAAAA,GAAG,CAACN,IAAD,CAAH;AACD,WAFD,MAEO;AACLM,YAAAA,GAAG,CAACnC,OAAJ,GAAc6B,IAAd;AACD;AACF;AACF,OAnH6C;;AAAA,oDAsH5CO,SAD6B,IAE1B;AACH,aAAKrB,MAAL,GAAcqB,SAAd;AAEApF,QAAAA,sBAAsB,CAACqF,oBAAvB,CACExB,IADF,EAEE,KAAKU,UAFP,EAGEa,SAHF;AAKD,OA/H6C;;AAAA,oDAiIdE,UAAD,IAAwB;AACrD,aAAKC,OAAL,GAAeD,UAAf;;AAEA,YAAI1F,QAAQ,CAAC6B,EAAT,KAAgB,KAApB,EAA2B;AACzB;AAEEzB,UAAAA,sBAAsB,CAACwF,oBADzB,CAGE,KAAKjB,UAHP,EAIEe,UAJF,EAKE1E,UAAU,CAAC6E,mBALb,EAKkC;AAChC,eAAKC,QANP;AAQD,SAVD,MAUO;AACLvF,UAAAA,yBAAyB,CAAC,KAAKoE,UAAN,EAAkB;AACzCC,YAAAA,cAAc,EAAE,KAAKjD,qBADoB;AAEzCoE,YAAAA,oBAAoB,EAAE,KAAKnE;AAFc,WAAlB,CAAzB;;AAKA,gBAAMoE,UAAU,GAAG,CAAC,MAAM;AAAA;;AACxB,kBAAMpB,cAAc,mBAAG,KAAK7B,KAAR,iDAAG,aAAY6B,cAAnC;AACA,kBAAMqB,uBAAuB,GAC3BrB,cAAc,KACb,aAAaA,cAAb,IACC,yBAAyBA,cAFb,CADhB;AAIA,kBAAMC,oBAAoB,mBAAG,KAAK9B,KAAR,iDAAG,aAAY8B,oBAAzC;AACA,kBAAMqB,2BAA2B,GAC/BrB,oBAAoB,KACnB,aAAaA,oBAAb,IACC,yBAAyBA,oBAFP,CADtB;AAIA,kBAAMsB,mBAAmB,GACvBF,uBAAuB,IAAIC,2BAD7B;;AAEA,gBAAIC,mBAAJ,EAAyB;AACvB;AACA,qBAAOnF,UAAU,CAACoF,kBAAlB;AACD,aAHD,MAGO,IAAIxB,cAAc,IAAI,gBAAgBA,cAAtC,EAAsD;AAC3D;AACA,qBAAO5D,UAAU,CAACqF,qBAAlB;AACD,aAHM,MAGA;AACL;AACA,qBAAOrF,UAAU,CAAC6E,mBAAlB;AACD;AACF,WAvBkB,GAAnB;;AAyBAzF,UAAAA,sBAAsB,CAACwF,oBAAvB,CACE,KAAKjB,UADP,EAEEe,UAFF,EAGEM,UAHF;AAKD;;AAEDrF,QAAAA,uBAAuB;AACxB,OArL6C;;AAAA,oDAwL5C6E,SAD6B,IAE1B;AACH,aAAKrB,MAAL,GAAcqB,SAAd;AAEApF,QAAAA,sBAAsB,CAACkG,oBAAvB,CAA4C,KAAK3B,UAAjD,EAA6Da,SAA7D;AACA7E,QAAAA,uBAAuB;AACxB,OA9L6C;;AAE5C,WAAKgE,UAAL,GAAkBnE,iBAAiB,EAAnC;AACA,WAAK2D,MAAL,GAAc,EAAd;AACA,WAAK2B,QAAL,gBAAgB/F,KAAK,CAACwG,SAAN,EAAhB;AACA,WAAKC,YAAL,gBAAoBzG,KAAK,CAACwG,SAAN,EAApB;AACA,WAAKzB,KAAL,GAAa;AAAEpC,QAAAA;AAAF,OAAb;;AACA,UAAIK,KAAK,CAAC0D,EAAV,EAAc;AACZ,YAAInG,cAAc,CAACyC,KAAK,CAAC0D,EAAP,CAAd,KAA6BhD,SAAjC,EAA4C;AAC1C,gBAAM,IAAIiD,KAAJ,CAAW,oBAAmB3D,KAAK,CAAC0D,EAAG,sBAAvC,CAAN;AACD;;AACDnG,QAAAA,cAAc,CAACyC,KAAK,CAAC0D,EAAP,CAAd,GAA2B,KAAK9B,UAAhC;AACD;AACF;;AAEDgC,IAAAA,iBAAiB,GAAG;AAClB,YAAM5D,KAAsB,GAAG,KAAKA,KAApC;AACA,WAAKyD,YAAL,CAAkBpD,OAAlB,GAA4B,IAA5B;;AAEA,UAAIT,cAAJ,EAAoB;AAClB,aAAKiE,uBAAL,GAA+B1G,kBAAkB,CAAC2C,WAAnB,CAC7B,wBAD6B,EAE7B,MAAM;AACJ,eAAKgE,QAAL,CAAeC,CAAD,KAAQ;AAAEpE,YAAAA;AAAF,WAAR,CAAd;AACA,eAAKqE,MAAL,CAAYhD,2BAAZ;AACD,SAL4B,CAA/B;AAOD;;AACD,UAAIjB,iBAAiB,CAACC,KAAD,CAArB,EAA8B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA5B,QAAAA,gBAAgB,CAAC,MAAM;AACrB,eAAK4F,MAAL,CAAYhD,2BAAZ;AACD,SAFe,CAAhB;AAGD;;AAED,WAAK0B,oBAAL,CACEhF,YAAY,CACV2D,cAAc,GAAGA,cAAc,CAAC,KAAKrB,KAAN,CAAjB,GAAgC,KAAKA,KADzC,EAEV,CAAC,GAAGmB,YAAJ,EAAkB,GAAGG,iBAArB,CAFU,EAGVF,MAHU,CADd;AAQA,WAAKyB,oBAAL,CAA0BlF,cAAc,CAAC,KAAKwE,QAAN,CAAxC,EAjCkB,CAiCkD;AACrE;;AAED8B,IAAAA,kBAAkB,GAAG;AACnB,YAAMrB,OAAO,GAAGjF,cAAc,CAAC,KAAKwE,QAAN,CAA9B;;AACA,UAAI,KAAKS,OAAL,KAAiBA,OAArB,EAA8B;AAC5B,aAAKC,oBAAL,CAA0BD,OAA1B,EAD4B,CACkB;AAC/C;;AACD,WAAKoB,MAAL,CAAYhD,2BAAZ;AACD;;AAEDkD,IAAAA,oBAAoB,GAAG;AAAA;;AACrB,oCAAKL,uBAAL,gFAA8BM,MAA9B;AACA,WAAKV,YAAL,CAAkBpD,OAAlB,GAA4B,KAA5B;AACAhD,MAAAA,sBAAsB,CAAC+G,kBAAvB,CAA0C,KAAKxC,UAA/C;AACAhE,MAAAA,uBAAuB,GAJF,CAKrB;;AACA,YAAMyG,SAA6B,GAAG,KAAKrE,KAAL,CAAW0D,EAAjD;;AACA,UAAIW,SAAJ,EAAe;AACb;AACA,eAAO9G,cAAc,CAAC8G,SAAD,CAArB;AACD;AACF;;AA0HOL,IAAAA,MAAM,CAACM,cAAD,EAAyB;AACrC,UAAI,CAAC,KAAKb,YAAL,CAAkBpD,OAAvB,EAAgC;AAC9B;AACD;;AAED,YAAML,KAAsB,GAAG,KAAKA,KAApC,CALqC,CAOrC;AACA;AACA;;AACA,UAAID,iBAAiB,CAACC,KAAD,CAAjB,IAA4BsE,cAAc,GAAG,CAAjD,EAAoD;AAClDlG,QAAAA,gBAAgB,CAAC,MAAM;AACrB,eAAK4F,MAAL,CAAYM,cAAc,GAAG,CAA7B;AACD,SAFe,CAAhB;AAGD,OAJD,MAIO;AACL,cAAM7B,SAAS,GAAG/E,YAAY,CAC5B2D,cAAc,GAAGA,cAAc,CAAC,KAAKrB,KAAN,CAAjB,GAAgC,KAAKA,KADvB,EAE5B,CAAC,GAAGmB,YAAJ,EAAkB,GAAGG,iBAArB,CAF4B,EAG5BF,MAH4B,CAA9B;;AAKA,YAAI,CAACvD,SAAS,CAAC,KAAKuD,MAAN,EAAcqB,SAAd,CAAd,EAAwC;AACtC,eAAKc,oBAAL,CAA0Bd,SAA1B;AACD;AACF;AACF;;AAED8B,IAAAA,cAAc,CAACC,OAAD,EAAe;AAC3B,YAAMC,WAAW,GAAG,EAAE,GAAG,KAAKzE,KAAV;AAAiB,WAAGwE;AAApB,OAApB;AACA,YAAM/B,SAAS,GAAG/E,YAAY,CAC5B2D,cAAc,GAAGA,cAAc,CAACoD,WAAD,CAAjB,GAAiCA,WADnB,EAE5B,CAAC,GAAGtD,YAAJ,EAAkB,GAAGG,iBAArB,CAF4B,EAG5BF,MAH4B,CAA9B;AAKA,WAAKmC,oBAAL,CAA0Bd,SAA1B;AACD;;AAEDiC,IAAAA,MAAM,GAAG;AAAA;;AACP,UAAI7E,OAAO,IAAI,CAAC,KAAK8E,OAAjB,IAA4B,CAAC5G,SAAS,EAAtC,IAA4Cd,QAAQ,CAAC6B,EAAT,KAAgB,KAAhE,EAAuE;AACrE,cAAM,IAAI6E,KAAJ,CACJzC,IAAI,GACF,yMAFE,CAAN;AAID;;AAED,UAAI0D,mBAAmB,GAAG,KAAKhG,qBAA/B,CARO,CASP;;AAKA,YAAM;AAAEiD,QAAAA,cAAF;AAAkBjD,QAAAA;AAAlB,UACJ,KAAKoB,KADP;;AAEA,UAAI6B,cAAc,IAAI,OAAOA,cAAP,KAA0B,UAAhD,EAA4D;AAC1D;AACA;AACA;AACA,YAAIjD,qBAAJ,EAA2B;AACzB,gBAAM,IAAI+E,KAAJ,CACJ,yEADI,CAAN;AAGD;;AACDiB,QAAAA,mBAAmB,GAAG/C,cAAtB;AACD,OAVD,MAUO;AACL,YACEjD,qBAAqB,IACrB,OAAOA,qBAAP,KAAiC,UAFnC,EAGE;AACA,gBAAM,IAAI+E,KAAJ,CACJ,yEADI,CAAN;AAGD;AACF;;AAED,UAAIkB,wBAAwB,GAAG,KAAKhG,2BAApC,CArCO,CAsCP;;AAKA,YAAM;AACJiD,QAAAA,oBADI;AAEJjD,QAAAA;AAFI,UAG4B,KAAKmB,KAHvC;;AAIA,UAAI8B,oBAAoB,IAAI,OAAOA,oBAAP,KAAgC,UAA5D,EAAwE;AACtE;AACA;AACA;AACA,YAAIjD,2BAAJ,EAAiC;AAC/B,gBAAM,IAAI8E,KAAJ,CACJ,yEADI,CAAN;AAGD;;AACDkB,QAAAA,wBAAwB,GAAG/C,oBAA3B;AACD,OAVD,MAUO;AACL,YACEjD,2BAA2B,IAC3B,OAAOA,2BAAP,KAAuC,UAFzC,EAGE;AACA,gBAAM,IAAI8E,KAAJ,CACJ,yEADI,CAAN;AAGD;AACF;;AACD,YAAMmB,MAAM,GAAG;AACblG,QAAAA,qBAAqB,EAAE,KAAKmD,KAAL,CAAWpC,YAAX,GACnBiF,mBADmB,GAEnBlE,SAHS;AAIb7B,QAAAA,2BAA2B,EAAE,KAAKkD,KAAL,CAAWpC,YAAX,GACzBkF,wBADyB,GAEzBnE;AANS,OAAf;AASA,WAAKqC,QAAL,CAAc1C,OAAd,GAAwByE,MAAxB;AAEA,UAAI1C,KAAU,GAAG,IAAjB;;AACA,UAAI;AACFA,QAAAA,KAAK,GAAGpF,KAAK,CAACqF,QAAN,CAAeC,IAAf,CAAoB,KAAKtC,KAAL,CAAWuC,QAA/B,CAAR;AACD,OAFD,CAEE,OAAOwC,CAAP,EAAU;AACV,cAAM,IAAIpB,KAAJ,CACJ3F,UAAU,CACP,GAAEkD,IAAK,4JADA,CADN,CAAN;AAKD;;AAED,UAAI8D,aAAa,GAAG5C,KAAK,CAACpC,KAAN,CAAYuC,QAAhC;;AACA,UACE1C,OAAO,IACPuC,KAAK,CAAC6C,IADN,KAEC7C,KAAK,CAAC6C,IAAN,KAAe,wBAAf,IACC7C,KAAK,CAAC6C,IAAN,CAAW/D,IAAX,KAAoB,MADrB,IAECkB,KAAK,CAAC6C,IAAN,CAAWC,WAAX,KAA2B,MAJ7B,CADF,EAME;AACAF,QAAAA,aAAa,GAAGhI,KAAK,CAACqF,QAAN,CAAe8C,OAAf,CAAuBH,aAAvB,CAAhB;AACAA,QAAAA,aAAa,CAACI,IAAd,eACE,oBAAC,qBAAD;AACE,UAAA,GAAG,EAAC,uBADN;AAEE,UAAA,KAAK,EAAC,mBAFR;AAGE,UAAA,OAAO,EAAEhD,KAAK,CAACpC,KAAN,CAAYqF;AAHvB,UADF;AAOD;;AAED,0BAAOrI,KAAK,CAACsI,YAAN,CACLlD,KADK,EAEL;AACEI,QAAAA,GAAG,EAAE,KAAK+C,UADZ;AAEEC,QAAAA,WAAW,EAAE,KAFf;AAGE,YAAIzH,SAAS,KACT;AACE0H,UAAAA,WAAW,EAAEvE,IADf;AAEEU,UAAAA,UAAU,EAAE,KAAKA,UAFnB;AAGE8D,UAAAA,OAAO,EAAE,KAAK1F,KAAL,CAAW0F;AAHtB,SADS,GAMT,EANJ,CAHF;AAUEC,QAAAA,MAAM,wBAAE,KAAK3F,KAAL,CAAW2F,MAAb,mEAAuBvD,KAAK,CAACpC,KAAN,CAAY2F,MAV3C;AAWE,WAAGb;AAXL,OAFK,EAeLE,aAfK,CAAP;AAiBD;;AA5WD;;AAPwE,kBAIpEzD,OAJoE,iBAQnDL,IARmD;;AAAA,kBAIpEK,OAJoE,iBASnDpD,6BATmD;;AAqX1E,SAAOoD,OAAP;AACD", "sourcesContent": ["import * as React from 'react';\nimport {\n  Platform,\n  UIManager,\n  DeviceEventEmitter,\n  EmitterSubscription,\n} from 'react-native';\nimport { customDirectEventTypes } from './customDirectEventTypes';\nimport RNGestureHandlerModule from '../RNGestureHandlerModule';\nimport { State } from '../State';\nimport { handlerIDToTag, registerOldGestureHandler } from './handlersRegistry';\nimport { getNextHandlerTag } from './getNextHandlerTag';\n\nimport {\n  BaseGestureHandlerProps,\n  GestureEvent,\n  HandlerStateChangeEvent,\n} from './gestureHandlerCommon';\nimport { filterConfig, findNodeHandle, scheduleFlushOperations } from './utils';\nimport { ValueOf } from '../typeUtils';\nimport { deepEqual, isFabric, isJestEnv, tagMessage } from '../utils';\nimport { ActionType } from '../ActionType';\nimport { PressabilityDebugView } from './PressabilityDebugView';\nimport GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';\nimport { ghQueueMicrotask } from '../ghQueueMicrotask';\n\nconst UIManagerAny = UIManager as any;\n\ncustomDirectEventTypes.topGestureHandlerEvent = {\n  registrationName: 'onGestureHandlerEvent',\n};\n\nconst customGHEventsConfigFabricAndroid = {\n  topOnGestureHandlerEvent: { registrationName: 'onGestureHandlerEvent' },\n  topOnGestureHandlerStateChange: {\n    registrationName: 'onGestureHandlerStateChange',\n  },\n};\n\nconst customGHEventsConfig = {\n  onGestureHandlerEvent: { registrationName: 'onGestureHandlerEvent' },\n  onGestureHandlerStateChange: {\n    registrationName: 'onGestureHandlerStateChange',\n  },\n\n  // When using React Native Gesture Handler for Animated.event with useNativeDriver: true\n  // on Android with Fabric enabled, the native part still sends the native events to JS\n  // but prefixed with \"top\". We cannot simply rename the events above so they are prefixed\n  // with \"top\" instead of \"on\" because in such case Animated.events would not be registered.\n  // That's why we need to register another pair of event names.\n  // The incoming events will be queued but never handled.\n  // Without this piece of code below, you'll get the following JS error:\n  // Unsupported top level event type \"topOnGestureHandlerEvent\" dispatched\n  ...(isFabric() &&\n    Platform.OS === 'android' &&\n    customGHEventsConfigFabricAndroid),\n};\n\n// Add gesture specific events to genericDirectEventTypes object exported from UIManager\n// native module.\n// Once new event types are registered with react it is possible to dispatch these\n// events to all kind of native views.\nUIManagerAny.genericDirectEventTypes = {\n  ...UIManagerAny.genericDirectEventTypes,\n  ...customGHEventsConfig,\n};\n// In newer versions of RN the `genericDirectEventTypes` is located in the object\n// returned by UIManager.getViewManagerConfig('getConstants') or in older RN UIManager.getConstants(), we need to add it there as well to make\n// it compatible with RN 61+\nconst UIManagerConstants =\n  UIManagerAny.getViewManagerConfig?.('getConstants') ??\n  UIManagerAny.getConstants?.();\n\nif (UIManagerConstants) {\n  UIManagerConstants.genericDirectEventTypes = {\n    ...UIManagerConstants.genericDirectEventTypes,\n    ...customGHEventsConfig,\n  };\n}\n\n// Wrap JS responder calls and notify gesture handler manager\nconst {\n  setJSResponder: oldSetJSResponder = () => {\n    // no-op\n  },\n  clearJSResponder: oldClearJSResponder = () => {\n    // no-op\n  },\n} = UIManagerAny;\nUIManagerAny.setJSResponder = (tag: number, blockNativeResponder: boolean) => {\n  RNGestureHandlerModule.handleSetJSResponder(tag, blockNativeResponder);\n  oldSetJSResponder(tag, blockNativeResponder);\n};\nUIManagerAny.clearJSResponder = () => {\n  RNGestureHandlerModule.handleClearJSResponder();\n  oldClearJSResponder();\n};\n\nlet allowTouches = true;\nconst DEV_ON_ANDROID = __DEV__ && Platform.OS === 'android';\n// Toggled inspector blocks touch events in order to allow inspecting on Android\n// This needs to be a global variable in order to set initial state for `allowTouches` property in Handler component\nif (DEV_ON_ANDROID) {\n  DeviceEventEmitter.addListener('toggleElementInspector', () => {\n    allowTouches = !allowTouches;\n  });\n}\n\ntype HandlerProps<T extends Record<string, unknown>> = Readonly<\n  React.PropsWithChildren<BaseGestureHandlerProps<T>>\n>;\nfunction hasUnresolvedRefs<T extends Record<string, unknown>>(\n  props: HandlerProps<T>\n) {\n  // TODO(TS) - add type for extract arg\n  const extract = (refs: any | any[]) => {\n    if (!Array.isArray(refs)) {\n      return refs && refs.current === null;\n    }\n    return refs.some((r) => r && r.current === null);\n  };\n  return extract(props['simultaneousHandlers']) || extract(props['waitFor']);\n}\n\nconst stateToPropMappings = {\n  [State.UNDETERMINED]: undefined,\n  [State.BEGAN]: 'onBegan',\n  [State.FAILED]: 'onFailed',\n  [State.CANCELLED]: 'onCancelled',\n  [State.ACTIVE]: 'onActivated',\n  [State.END]: 'onEnded',\n} as const;\n\ntype CreateHandlerArgs<HandlerPropsT extends Record<string, unknown>> =\n  Readonly<{\n    name: string;\n    allowedProps: Readonly<Extract<keyof HandlerPropsT, string>[]>;\n    config: Readonly<Record<string, unknown>>;\n    transformProps?: (props: HandlerPropsT) => HandlerPropsT;\n    customNativeProps?: Readonly<string[]>;\n  }>;\n\n// TODO(TS) fix event types\ntype InternalEventHandlers = {\n  onGestureHandlerEvent?: (event: any) => void;\n  onGestureHandlerStateChange?: (event: any) => void;\n};\n\ntype AttachGestureHandlerWeb = (\n  handlerTag: number,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  newView: any,\n  _actionType: ActionType,\n  propsRef: React.RefObject<unknown>\n) => void;\n\nconst UNRESOLVED_REFS_RETRY_LIMIT = 1;\n\n// TODO(TS) - make sure that BaseGestureHandlerProps doesn't need other generic parameter to work with custom properties.\nexport default function createHandler<\n  T extends BaseGestureHandlerProps<U>,\n  U extends Record<string, unknown>\n>({\n  name,\n  allowedProps = [],\n  config = {},\n  transformProps,\n  customNativeProps = [],\n}: CreateHandlerArgs<T>): React.ComponentType<T & React.RefAttributes<any>> {\n  interface HandlerState {\n    allowTouches: boolean;\n  }\n  class Handler extends React.Component<\n    T & InternalEventHandlers,\n    HandlerState\n  > {\n    static displayName = name;\n    static contextType = GestureHandlerRootViewContext;\n\n    private handlerTag: number;\n    private config: Record<string, unknown>;\n    private propsRef: React.MutableRefObject<unknown>;\n    private isMountedRef: React.MutableRefObject<boolean | null>;\n    private viewNode: any;\n    private viewTag?: number;\n    private inspectorToggleListener?: EmitterSubscription;\n\n    constructor(props: T & InternalEventHandlers) {\n      super(props);\n      this.handlerTag = getNextHandlerTag();\n      this.config = {};\n      this.propsRef = React.createRef();\n      this.isMountedRef = React.createRef();\n      this.state = { allowTouches };\n      if (props.id) {\n        if (handlerIDToTag[props.id] !== undefined) {\n          throw new Error(`Handler with ID \"${props.id}\" already registered`);\n        }\n        handlerIDToTag[props.id] = this.handlerTag;\n      }\n    }\n\n    componentDidMount() {\n      const props: HandlerProps<U> = this.props;\n      this.isMountedRef.current = true;\n\n      if (DEV_ON_ANDROID) {\n        this.inspectorToggleListener = DeviceEventEmitter.addListener(\n          'toggleElementInspector',\n          () => {\n            this.setState((_) => ({ allowTouches }));\n            this.update(UNRESOLVED_REFS_RETRY_LIMIT);\n          }\n        );\n      }\n      if (hasUnresolvedRefs(props)) {\n        // If there are unresolved refs (e.g. \".current\" has not yet been set)\n        // passed as `simultaneousHandlers` or `waitFor`, we enqueue a call to\n        // _update method that will try to update native handler props using\n        // queueMicrotask. This makes it so update() function gets called after all\n        // react components are mounted and we expect the missing ref object to\n        // be resolved by then.\n        ghQueueMicrotask(() => {\n          this.update(UNRESOLVED_REFS_RETRY_LIMIT);\n        });\n      }\n\n      this.createGestureHandler(\n        filterConfig(\n          transformProps ? transformProps(this.props) : this.props,\n          [...allowedProps, ...customNativeProps],\n          config\n        )\n      );\n\n      this.attachGestureHandler(findNodeHandle(this.viewNode) as number); // TODO(TS) - check if this can be null\n    }\n\n    componentDidUpdate() {\n      const viewTag = findNodeHandle(this.viewNode);\n      if (this.viewTag !== viewTag) {\n        this.attachGestureHandler(viewTag as number); // TODO(TS) - check interaction between _viewTag & findNodeHandle\n      }\n      this.update(UNRESOLVED_REFS_RETRY_LIMIT);\n    }\n\n    componentWillUnmount() {\n      this.inspectorToggleListener?.remove();\n      this.isMountedRef.current = false;\n      RNGestureHandlerModule.dropGestureHandler(this.handlerTag);\n      scheduleFlushOperations();\n      // We can't use this.props.id directly due to TS generic type narrowing bug, see https://github.com/microsoft/TypeScript/issues/13995 for more context\n      const handlerID: string | undefined = this.props.id;\n      if (handlerID) {\n        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n        delete handlerIDToTag[handlerID];\n      }\n    }\n\n    private onGestureHandlerEvent = (event: GestureEvent<U>) => {\n      if (event.nativeEvent.handlerTag === this.handlerTag) {\n        if (typeof this.props.onGestureEvent === 'function') {\n          this.props.onGestureEvent?.(event);\n        }\n      } else {\n        this.props.onGestureHandlerEvent?.(event);\n      }\n    };\n\n    // TODO(TS) - make sure this is right type for event\n    private onGestureHandlerStateChange = (\n      event: HandlerStateChangeEvent<U>\n    ) => {\n      if (event.nativeEvent.handlerTag === this.handlerTag) {\n        if (typeof this.props.onHandlerStateChange === 'function') {\n          this.props.onHandlerStateChange?.(event);\n        }\n\n        const state: ValueOf<typeof State> = event.nativeEvent.state;\n        const stateEventName = stateToPropMappings[state];\n        const eventHandler = stateEventName && this.props[stateEventName];\n        if (eventHandler && typeof eventHandler === 'function') {\n          eventHandler(event);\n        }\n      } else {\n        this.props.onGestureHandlerStateChange?.(event);\n      }\n    };\n\n    private refHandler = (node: any) => {\n      this.viewNode = node;\n\n      const child = React.Children.only(this.props.children);\n      // TODO(TS) fix ref type\n      const { ref }: any = child;\n      if (ref !== null) {\n        if (typeof ref === 'function') {\n          ref(node);\n        } else {\n          ref.current = node;\n        }\n      }\n    };\n\n    private createGestureHandler = (\n      newConfig: Readonly<Record<string, unknown>>\n    ) => {\n      this.config = newConfig;\n\n      RNGestureHandlerModule.createGestureHandler(\n        name,\n        this.handlerTag,\n        newConfig\n      );\n    };\n\n    private attachGestureHandler = (newViewTag: number) => {\n      this.viewTag = newViewTag;\n\n      if (Platform.OS === 'web') {\n        // Typecast due to dynamic resolution, attachGestureHandler should have web version signature in this branch\n        (\n          RNGestureHandlerModule.attachGestureHandler as AttachGestureHandlerWeb\n        )(\n          this.handlerTag,\n          newViewTag,\n          ActionType.JS_FUNCTION_OLD_API, // ignored on web\n          this.propsRef\n        );\n      } else {\n        registerOldGestureHandler(this.handlerTag, {\n          onGestureEvent: this.onGestureHandlerEvent,\n          onGestureStateChange: this.onGestureHandlerStateChange,\n        });\n\n        const actionType = (() => {\n          const onGestureEvent = this.props?.onGestureEvent;\n          const isGestureHandlerWorklet =\n            onGestureEvent &&\n            ('current' in onGestureEvent ||\n              'workletEventHandler' in onGestureEvent);\n          const onHandlerStateChange = this.props?.onHandlerStateChange;\n          const isStateChangeHandlerWorklet =\n            onHandlerStateChange &&\n            ('current' in onHandlerStateChange ||\n              'workletEventHandler' in onHandlerStateChange);\n          const isReanimatedHandler =\n            isGestureHandlerWorklet || isStateChangeHandlerWorklet;\n          if (isReanimatedHandler) {\n            // Reanimated worklet\n            return ActionType.REANIMATED_WORKLET;\n          } else if (onGestureEvent && '__isNative' in onGestureEvent) {\n            // Animated.event with useNativeDriver: true\n            return ActionType.NATIVE_ANIMATED_EVENT;\n          } else {\n            // JS callback or Animated.event with useNativeDriver: false\n            return ActionType.JS_FUNCTION_OLD_API;\n          }\n        })();\n\n        RNGestureHandlerModule.attachGestureHandler(\n          this.handlerTag,\n          newViewTag,\n          actionType\n        );\n      }\n\n      scheduleFlushOperations();\n    };\n\n    private updateGestureHandler = (\n      newConfig: Readonly<Record<string, unknown>>\n    ) => {\n      this.config = newConfig;\n\n      RNGestureHandlerModule.updateGestureHandler(this.handlerTag, newConfig);\n      scheduleFlushOperations();\n    };\n\n    private update(remainingTries: number) {\n      if (!this.isMountedRef.current) {\n        return;\n      }\n\n      const props: HandlerProps<U> = this.props;\n\n      // When ref is set via a function i.e. `ref={(r) => refObject.current = r}` instead of\n      // `ref={refObject}` it's possible that it won't be resolved in time. Seems like trying\n      // again is easy enough fix.\n      if (hasUnresolvedRefs(props) && remainingTries > 0) {\n        ghQueueMicrotask(() => {\n          this.update(remainingTries - 1);\n        });\n      } else {\n        const newConfig = filterConfig(\n          transformProps ? transformProps(this.props) : this.props,\n          [...allowedProps, ...customNativeProps],\n          config\n        );\n        if (!deepEqual(this.config, newConfig)) {\n          this.updateGestureHandler(newConfig);\n        }\n      }\n    }\n\n    setNativeProps(updates: any) {\n      const mergedProps = { ...this.props, ...updates };\n      const newConfig = filterConfig(\n        transformProps ? transformProps(mergedProps) : mergedProps,\n        [...allowedProps, ...customNativeProps],\n        config\n      );\n      this.updateGestureHandler(newConfig);\n    }\n\n    render() {\n      if (__DEV__ && !this.context && !isJestEnv() && Platform.OS !== 'web') {\n        throw new Error(\n          name +\n            ' must be used as a descendant of GestureHandlerRootView. Otherwise the gestures will not be recognized. See https://docs.swmansion.com/react-native-gesture-handler/docs/installation for more details.'\n        );\n      }\n\n      let gestureEventHandler = this.onGestureHandlerEvent;\n      // Another instance of https://github.com/microsoft/TypeScript/issues/13995\n      type OnGestureEventHandlers = {\n        onGestureEvent?: BaseGestureHandlerProps<U>['onGestureEvent'];\n        onGestureHandlerEvent?: InternalEventHandlers['onGestureHandlerEvent'];\n      };\n      const { onGestureEvent, onGestureHandlerEvent }: OnGestureEventHandlers =\n        this.props;\n      if (onGestureEvent && typeof onGestureEvent !== 'function') {\n        // If it's not a method it should be an native Animated.event\n        // object. We set it directly as the handler for the view\n        // In this case nested handlers are not going to be supported\n        if (onGestureHandlerEvent) {\n          throw new Error(\n            'Nesting touch handlers with native animated driver is not supported yet'\n          );\n        }\n        gestureEventHandler = onGestureEvent;\n      } else {\n        if (\n          onGestureHandlerEvent &&\n          typeof onGestureHandlerEvent !== 'function'\n        ) {\n          throw new Error(\n            'Nesting touch handlers with native animated driver is not supported yet'\n          );\n        }\n      }\n\n      let gestureStateEventHandler = this.onGestureHandlerStateChange;\n      // Another instance of https://github.com/microsoft/TypeScript/issues/13995\n      type OnGestureStateChangeHandlers = {\n        onHandlerStateChange?: BaseGestureHandlerProps<U>['onHandlerStateChange'];\n        onGestureHandlerStateChange?: InternalEventHandlers['onGestureHandlerStateChange'];\n      };\n      const {\n        onHandlerStateChange,\n        onGestureHandlerStateChange,\n      }: OnGestureStateChangeHandlers = this.props;\n      if (onHandlerStateChange && typeof onHandlerStateChange !== 'function') {\n        // If it's not a method it should be an native Animated.event\n        // object. We set it directly as the handler for the view\n        // In this case nested handlers are not going to be supported\n        if (onGestureHandlerStateChange) {\n          throw new Error(\n            'Nesting touch handlers with native animated driver is not supported yet'\n          );\n        }\n        gestureStateEventHandler = onHandlerStateChange;\n      } else {\n        if (\n          onGestureHandlerStateChange &&\n          typeof onGestureHandlerStateChange !== 'function'\n        ) {\n          throw new Error(\n            'Nesting touch handlers with native animated driver is not supported yet'\n          );\n        }\n      }\n      const events = {\n        onGestureHandlerEvent: this.state.allowTouches\n          ? gestureEventHandler\n          : undefined,\n        onGestureHandlerStateChange: this.state.allowTouches\n          ? gestureStateEventHandler\n          : undefined,\n      };\n\n      this.propsRef.current = events;\n\n      let child: any = null;\n      try {\n        child = React.Children.only(this.props.children);\n      } catch (e) {\n        throw new Error(\n          tagMessage(\n            `${name} got more than one view as a child. If you want the gesture to work on multiple views, wrap them with a common parent and attach the gesture to that view.`\n          )\n        );\n      }\n\n      let grandChildren = child.props.children;\n      if (\n        __DEV__ &&\n        child.type &&\n        (child.type === 'RNGestureHandlerButton' ||\n          child.type.name === 'View' ||\n          child.type.displayName === 'View')\n      ) {\n        grandChildren = React.Children.toArray(grandChildren);\n        grandChildren.push(\n          <PressabilityDebugView\n            key=\"pressabilityDebugView\"\n            color=\"mediumspringgreen\"\n            hitSlop={child.props.hitSlop}\n          />\n        );\n      }\n\n      return React.cloneElement(\n        child,\n        {\n          ref: this.refHandler,\n          collapsable: false,\n          ...(isJestEnv()\n            ? {\n                handlerType: name,\n                handlerTag: this.handlerTag,\n                enabled: this.props.enabled,\n              }\n            : {}),\n          testID: this.props.testID ?? child.props.testID,\n          ...events,\n        },\n        grandChildren\n      );\n    }\n  }\n  return Handler;\n}\n"]}