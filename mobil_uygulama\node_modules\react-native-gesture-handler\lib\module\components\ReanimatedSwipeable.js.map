{"version": 3, "sources": ["ReanimatedSwipeable.tsx"], "names": ["React", "forwardRef", "useCallback", "useImperativeHandle", "useRef", "GestureObjects", "Gesture", "GestureDetector", "Animated", "Extrapolation", "interpolate", "runOnJS", "useAnimatedStyle", "useSharedValue", "with<PERSON><PERSON><PERSON>", "I18nManager", "StyleSheet", "View", "DRAG_TOSS", "Swipeable", "props", "ref", "leftThreshold", "rightT<PERSON><PERSON><PERSON>", "onSwipeableOpenStartDrag", "onSwipeableCloseStartDrag", "enableTrackpadTwoFingerGesture", "enabled", "containerStyle", "childrenContainerStyle", "animationOptions", "overshootLeft", "overshootRight", "onSwipeableWillOpen", "onSwipeableWillClose", "onSwipeableOpen", "onSwipeableClose", "testID", "remainingProps", "rowState", "userDrag", "appliedTranslation", "row<PERSON>id<PERSON>", "leftWidth", "rightWidth", "rightOffset", "leftActionTranslate", "rightActionTranslate", "showLeftProgress", "showRightProgress", "swipeableMethods", "close", "openLeft", "openRight", "reset", "defaultProps", "friction", "overshootFriction", "overshootLeftProp", "overshootRightProp", "calculateCurrentOffset", "value", "updateAnimatedEvent", "Math", "max", "startOffset", "offsetDrag", "Number", "MIN_VALUE", "CLAMP", "dispatchImmediateEvents", "fromValue", "toValue", "closingDirection", "dispatchEndEvents", "current", "animationOptionsProp", "animateRow", "velocityX", "sign", "translationSpringConfig", "duration", "dampingRatio", "stiffness", "velocity", "overshootClamping", "progressSpringConfig", "isFinished", "progressTarget", "onRowLayout", "nativeEvent", "layout", "width", "children", "renderLeftActions", "renderRightActions", "dragOffsetFromLeftEdge", "dragOffsetFromRightEdge", "leftAnimatedStyle", "transform", "translateX", "leftElement", "styles", "leftActions", "x", "rightAnimatedStyle", "rightElement", "rightActions", "leftThresholdProp", "rightThresholdProp", "handleRelease", "event", "translationX", "startOffsetX", "tapGesture", "Tap", "onStart", "panGesture", "Pan", "onUpdate", "direction", "onEnd", "activeOffsetX", "shouldCancelWhenOutside", "animatedStyle", "pointerEvents", "swipeableComponent", "container", "create", "overflow", "absoluteFillObject", "flexDirection", "isRTL"], "mappings": ";;AAAA;AACA;AACA;AAEA,OAAOA,KAAP,IAEEC,UAFF,EAGEC,WAHF,EAIEC,mBAJF,EAKEC,MALF,QAMO,OANP;AAOA,SAASC,cAAc,IAAIC,OAA3B,QAA0C,qCAA1C;AACA,SAASC,eAAT,QAAgC,sCAAhC;AAOA,OAAOC,QAAP,IACEC,aADF,EAGEC,WAHF,EAIEC,OAJF,EAKEC,gBALF,EAMEC,cANF,EAOEC,UAPF,QAQO,yBARP;AASA,SACEC,WADF,EAIEC,UAJF,EAKEC,IALF,QAOO,cAPP;AASA,MAAMC,SAAS,GAAG,IAAlB;AA+JA,MAAMC,SAAS,gBAAGlB,UAAU,CAC1B,SAASkB,SAAT,CACEC,KADF,EAEEC,GAFF,EAGE;AACA,QAAM;AACJC,IAAAA,aADI;AAEJC,IAAAA,cAFI;AAGJC,IAAAA,wBAHI;AAIJC,IAAAA,yBAJI;AAKJC,IAAAA,8BALI;AAMJC,IAAAA,OANI;AAOJC,IAAAA,cAPI;AAQJC,IAAAA,sBARI;AASJC,IAAAA,gBATI;AAUJC,IAAAA,aAVI;AAWJC,IAAAA,cAXI;AAYJC,IAAAA,mBAZI;AAaJC,IAAAA,oBAbI;AAcJC,IAAAA,eAdI;AAeJC,IAAAA,gBAfI;AAgBJC,IAAAA,MAhBI;AAiBJ,OAAGC;AAjBC,MAkBFlB,KAlBJ;AAoBA,QAAMmB,QAAQ,GAAG1B,cAAc,CAAS,CAAT,CAA/B;AAEA,QAAM2B,QAAQ,GAAG3B,cAAc,CAAS,CAAT,CAA/B;AACA,QAAM4B,kBAAkB,GAAG5B,cAAc,CAAS,CAAT,CAAzC;AAEA,QAAM6B,QAAQ,GAAG7B,cAAc,CAAS,CAAT,CAA/B;AACA,QAAM8B,SAAS,GAAG9B,cAAc,CAAS,CAAT,CAAhC;AACA,QAAM+B,UAAU,GAAG/B,cAAc,CAAS,CAAT,CAAjC;AACA,QAAMgC,WAAW,GAAGhC,cAAc,CAAS,CAAT,CAAlC;AAEA,QAAMiC,mBAAmB,GAAGjC,cAAc,CAAS,CAAT,CAA1C;AACA,QAAMkC,oBAAoB,GAAGlC,cAAc,CAAS,CAAT,CAA3C;AAEA,QAAMmC,gBAAgB,GAAGnC,cAAc,CAAS,CAAT,CAAvC;AACA,QAAMoC,iBAAiB,GAAGpC,cAAc,CAAS,CAAT,CAAxC;AAEA,QAAMqC,gBAAgB,GAAG9C,MAAM,CAAmB;AAChD+C,IAAAA,KAAK,EAAE,MAAM;AACX;AACD,KAH+C;AAIhDC,IAAAA,QAAQ,EAAE,MAAM;AACd;AACD,KAN+C;AAOhDC,IAAAA,SAAS,EAAE,MAAM;AACf;AACD,KAT+C;AAUhDC,IAAAA,KAAK,EAAE,MAAM;AACX;AACD;AAZ+C,GAAnB,CAA/B;AAeA,QAAMC,YAAY,GAAG;AACnBC,IAAAA,QAAQ,EAAE,CADS;AAEnBC,IAAAA,iBAAiB,EAAE;AAFA,GAArB;AAKA,QAAM;AACJD,IAAAA,QAAQ,GAAGD,YAAY,CAACC,QADpB;AAEJC,IAAAA,iBAAiB,GAAGF,YAAY,CAACE;AAF7B,MAGFrC,KAHJ;AAKA,QAAMsC,iBAAiB,GAAG3B,aAA1B;AACA,QAAM4B,kBAAkB,GAAG3B,cAA3B;AAEA,QAAM4B,sBAAsB,GAAG1D,WAAW,CAAC,MAAM;AAC/C;;AACA,QAAIqC,QAAQ,CAACsB,KAAT,KAAmB,CAAvB,EAA0B;AACxB,aAAOlB,SAAS,CAACkB,KAAjB;AACD,KAFD,MAEO,IAAItB,QAAQ,CAACsB,KAAT,KAAmB,CAAC,CAAxB,EAA2B;AAChC,aAAO,CAACnB,QAAQ,CAACmB,KAAV,GAAkBhB,WAAW,CAACgB,KAArC;AACD;;AACD,WAAO,CAAP;AACD,GARyC,EAQvC,CAAClB,SAAD,EAAYE,WAAZ,EAAyBN,QAAzB,EAAmCG,QAAnC,CARuC,CAA1C;;AAUA,QAAMoB,mBAAmB,GAAG,MAAM;AAChC;;AACAlB,IAAAA,UAAU,CAACiB,KAAX,GAAmBE,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYtB,QAAQ,CAACmB,KAAT,GAAiBhB,WAAW,CAACgB,KAAzC,CAAnB;AAEA,UAAM9B,aAAa,GAAG2B,iBAAH,aAAGA,iBAAH,cAAGA,iBAAH,GAAwBf,SAAS,CAACkB,KAAV,GAAkB,CAA7D;AACA,UAAM7B,cAAc,GAAG2B,kBAAH,aAAGA,kBAAH,cAAGA,kBAAH,GAAyBf,UAAU,CAACiB,KAAX,GAAmB,CAAhE;AAEA,UAAMI,WAAW,GACf1B,QAAQ,CAACsB,KAAT,KAAmB,CAAnB,GACIlB,SAAS,CAACkB,KADd,GAEItB,QAAQ,CAACsB,KAAT,KAAmB,CAAC,CAApB,GACA,CAACjB,UAAU,CAACiB,KADZ,GAEA,CALN;AAOA,UAAMK,UAAU,GAAG1B,QAAQ,CAACqB,KAAT,GAAiBL,QAAjB,GAA4BS,WAA/C;AAEAxB,IAAAA,kBAAkB,CAACoB,KAAnB,GAA2BnD,WAAW,CACpCwD,UADoC,EAEpC,CACE,CAACtB,UAAU,CAACiB,KAAZ,GAAoB,CADtB,EAEE,CAACjB,UAAU,CAACiB,KAFd,EAGElB,SAAS,CAACkB,KAHZ,EAIElB,SAAS,CAACkB,KAAV,GAAkB,CAJpB,CAFoC,EAQpC,CACE,CAACjB,UAAU,CAACiB,KAAZ,IAAqB7B,cAAc,GAAG,IAAIyB,iBAAP,GAA2B,CAA9D,CADF,EAEE,CAACb,UAAU,CAACiB,KAFd,EAGElB,SAAS,CAACkB,KAHZ,EAIElB,SAAS,CAACkB,KAAV,IAAmB9B,aAAa,GAAG,IAAI0B,iBAAP,GAA2B,CAA3D,CAJF,CARoC,CAAtC;AAgBAT,IAAAA,gBAAgB,CAACa,KAAjB,GACElB,SAAS,CAACkB,KAAV,GAAkB,CAAlB,GACInD,WAAW,CACT+B,kBAAkB,CAACoB,KADV,EAET,CAAC,CAAC,CAAF,EAAK,CAAL,EAAQlB,SAAS,CAACkB,KAAlB,CAFS,EAGT,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAHS,CADf,GAMI,CAPN;AAQAf,IAAAA,mBAAmB,CAACe,KAApB,GAA4BnD,WAAW,CACrCsC,gBAAgB,CAACa,KADoB,EAErC,CAAC,CAAD,EAAIM,MAAM,CAACC,SAAX,CAFqC,EAGrC,CAAC,CAAC,KAAF,EAAS,CAAT,CAHqC,EAIrC3D,aAAa,CAAC4D,KAJuB,CAAvC;AAMApB,IAAAA,iBAAiB,CAACY,KAAlB,GACEjB,UAAU,CAACiB,KAAX,GAAmB,CAAnB,GACInD,WAAW,CACT+B,kBAAkB,CAACoB,KADV,EAET,CAAC,CAACjB,UAAU,CAACiB,KAAb,EAAoB,CAApB,EAAuB,CAAvB,CAFS,EAGT,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAHS,CADf,GAMI,CAPN;AAQAd,IAAAA,oBAAoB,CAACc,KAArB,GAA6BnD,WAAW,CACtCuC,iBAAiB,CAACY,KADoB,EAEtC,CAAC,CAAD,EAAIM,MAAM,CAACC,SAAX,CAFsC,EAGtC,CAAC,CAAC,KAAF,EAAS,CAAT,CAHsC,EAItC3D,aAAa,CAAC4D,KAJwB,CAAxC;AAMD,GA5DD;;AA8DA,QAAMC,uBAAuB,GAAGpE,WAAW,CACzC,CAACqE,SAAD,EAAoBC,OAApB,KAAwC;AACtC,QAAIA,OAAO,GAAG,CAAV,IAAevC,mBAAnB,EAAwC;AACtCA,MAAAA,mBAAmB,CAAC,MAAD,CAAnB;AACD,KAFD,MAEO,IAAIuC,OAAO,GAAG,CAAV,IAAevC,mBAAnB,EAAwC;AAC7CA,MAAAA,mBAAmB,CAAC,OAAD,CAAnB;AACD,KAFM,MAEA,IAAIC,oBAAJ,EAA0B;AAC/B,YAAMuC,gBAAgB,GAAGF,SAAS,GAAG,CAAZ,GAAgB,MAAhB,GAAyB,OAAlD;AACArC,MAAAA,oBAAoB,CAACuC,gBAAD,CAApB;AACD;AACF,GAVwC,EAWzC,CAACvC,oBAAD,EAAuBD,mBAAvB,CAXyC,CAA3C;AAcA,QAAMyC,iBAAiB,GAAGxE,WAAW,CACnC,CAACqE,SAAD,EAAoBC,OAApB,KAAwC;AACtC,QAAIA,OAAO,GAAG,CAAV,IAAerC,eAAnB,EAAoC;AAClCA,MAAAA,eAAe,CAAC,MAAD,EAASe,gBAAgB,CAACyB,OAA1B,CAAf;AACD,KAFD,MAEO,IAAIH,OAAO,GAAG,CAAV,IAAerC,eAAnB,EAAoC;AACzCA,MAAAA,eAAe,CAAC,OAAD,EAAUe,gBAAgB,CAACyB,OAA3B,CAAf;AACD,KAFM,MAEA,IAAIvC,gBAAJ,EAAsB;AAC3B,YAAMqC,gBAAgB,GAAGF,SAAS,GAAG,CAAZ,GAAgB,MAAhB,GAAyB,OAAlD;AACAnC,MAAAA,gBAAgB,CAACqC,gBAAD,EAAmBvB,gBAAgB,CAACyB,OAApC,CAAhB;AACD;AACF,GAVkC,EAWnC,CAACvC,gBAAD,EAAmBD,eAAnB,CAXmC,CAArC;AAcA,QAAMyC,oBAAoB,GAAG9C,gBAA7B;AAEA,QAAM+C,UAAU,GAAG3E,WAAW,CAC5B,CAACqE,SAAD,EAAoBC,OAApB,EAAqCM,SAArC,KAA4D;AAC1D;;AACAvC,IAAAA,QAAQ,CAACsB,KAAT,GAAiBE,IAAI,CAACgB,IAAL,CAAUP,OAAV,CAAjB;AAEA,UAAMQ,uBAAuB,GAAG;AAC9BC,MAAAA,QAAQ,EAAE,IADoB;AAE9BC,MAAAA,YAAY,EAAE,GAFgB;AAG9BC,MAAAA,SAAS,EAAE,GAHmB;AAI9BC,MAAAA,QAAQ,EAAEN,SAJoB;AAK9BO,MAAAA,iBAAiB,EAAE,IALW;AAM9B,SAAGT;AAN2B,KAAhC;AASA,UAAMU,oBAAoB,GAAG,EAC3B,GAAGN,uBADwB;AAE3BI,MAAAA,QAAQ,EAAE;AAFiB,KAA7B;AAKA3C,IAAAA,kBAAkB,CAACoB,KAAnB,GAA2B/C,UAAU,CACnC0D,OADmC,EAEnCQ,uBAFmC,EAGlCO,UAAD,IAAgB;AACd,UAAIA,UAAJ,EAAgB;AACd5E,QAAAA,OAAO,CAAC+D,iBAAD,CAAP,CAA2BH,SAA3B,EAAsCC,OAAtC;AACD;AACF,KAPkC,CAArC;AAUA,UAAMgB,cAAc,GAAGhB,OAAO,KAAK,CAAZ,GAAgB,CAAhB,GAAoB,CAA3C;AAEAxB,IAAAA,gBAAgB,CAACa,KAAjB,GACElB,SAAS,CAACkB,KAAV,GAAkB,CAAlB,GACI/C,UAAU,CAAC0E,cAAD,EAAiBF,oBAAjB,CADd,GAEI,CAHN;AAIArC,IAAAA,iBAAiB,CAACY,KAAlB,GACEjB,UAAU,CAACiB,KAAX,GAAmB,CAAnB,GACI/C,UAAU,CAAC0E,cAAD,EAAiBF,oBAAjB,CADd,GAEI,CAHN;AAKA3E,IAAAA,OAAO,CAAC2D,uBAAD,CAAP,CAAiCC,SAAjC,EAA4CC,OAA5C;AACD,GAzC2B,EA0C5B,CACEjC,QADF,EAEEqC,oBAFF,EAGEnC,kBAHF,EAIEO,gBAJF,EAKEL,SAAS,CAACkB,KALZ,EAMEZ,iBANF,EAOEL,UAAU,CAACiB,KAPb,EAQES,uBARF,EASEI,iBATF,CA1C4B,CAA9B;;AAuDA,QAAMe,WAAW,GAAG,CAAC;AAAEC,IAAAA;AAAF,GAAD,KAAwC;AAC1DhD,IAAAA,QAAQ,CAACmB,KAAT,GAAiB6B,WAAW,CAACC,MAAZ,CAAmBC,KAApC;AACD,GAFD;;AAIA,QAAM;AACJC,IAAAA,QADI;AAEJC,IAAAA,iBAFI;AAGJC,IAAAA,kBAHI;AAIJC,IAAAA,sBAAsB,GAAG,EAJrB;AAKJC,IAAAA,uBAAuB,GAAG;AALtB,MAMF7E,KANJ;AAQA8B,EAAAA,gBAAgB,CAACyB,OAAjB,GAA2B;AACzBxB,IAAAA,KAAK,GAAG;AACN;;AACA0B,MAAAA,UAAU,CAACjB,sBAAsB,EAAvB,EAA2B,CAA3B,CAAV;AACD,KAJwB;;AAKzBR,IAAAA,QAAQ,GAAG;AACT;;AACAyB,MAAAA,UAAU,CAACjB,sBAAsB,EAAvB,EAA2BjB,SAAS,CAACkB,KAArC,CAAV;AACD,KARwB;;AASzBR,IAAAA,SAAS,GAAG;AACV;;AACAT,MAAAA,UAAU,CAACiB,KAAX,GAAmBnB,QAAQ,CAACmB,KAAT,GAAiBhB,WAAW,CAACgB,KAAhD;AACAgB,MAAAA,UAAU,CAACjB,sBAAsB,EAAvB,EAA2B,CAAChB,UAAU,CAACiB,KAAvC,CAAV;AACD,KAbwB;;AAczBP,IAAAA,KAAK,GAAG;AACN;;AACAd,MAAAA,QAAQ,CAACqB,KAAT,GAAiB,CAAjB;AACAb,MAAAA,gBAAgB,CAACa,KAAjB,GAAyB,CAAzB;AACApB,MAAAA,kBAAkB,CAACoB,KAAnB,GAA2B,CAA3B;AACAtB,MAAAA,QAAQ,CAACsB,KAAT,GAAiB,CAAjB;AACD;;AApBwB,GAA3B;AAuBA,QAAMqC,iBAAiB,GAAGtF,gBAAgB,CACxC,OAAO;AACLuF,IAAAA,SAAS,EAAE,CACT;AACEC,MAAAA,UAAU,EAAEtD,mBAAmB,CAACe;AADlC,KADS;AADN,GAAP,CADwC,EAQxC,CAACf,mBAAD,CARwC,CAA1C;AAWA,QAAMuD,WAAW,GAAGP,iBAAiB,iBACnC,oBAAC,QAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAE,CAACQ,MAAM,CAACC,WAAR,EAAqBL,iBAArB;AAAtB,KACGJ,iBAAiB,CAChB9C,gBADgB,EAEhBP,kBAFgB,EAGhBS,gBAAgB,CAACyB,OAHD,CADpB,eAME,oBAAC,IAAD;AACE,IAAA,QAAQ,EAAE,CAAC;AAAEe,MAAAA;AAAF,KAAD,KACP/C,SAAS,CAACkB,KAAV,GAAkB6B,WAAW,CAACC,MAAZ,CAAmBa;AAF1C,IANF,CADF;AAeA,QAAMC,kBAAkB,GAAG7F,gBAAgB,CACzC,OAAO;AACLuF,IAAAA,SAAS,EAAE,CACT;AACEC,MAAAA,UAAU,EAAErD,oBAAoB,CAACc;AADnC,KADS;AADN,GAAP,CADyC,EAQzC,CAACd,oBAAD,CARyC,CAA3C;AAWA,QAAM2D,YAAY,GAAGX,kBAAkB,iBACrC,oBAAC,QAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAE,CAACO,MAAM,CAACK,YAAR,EAAsBF,kBAAtB;AAAtB,KACGV,kBAAkB,CACjB9C,iBADiB,EAEjBR,kBAFiB,EAGjBS,gBAAgB,CAACyB,OAHA,CADrB,eAME,oBAAC,IAAD;AACE,IAAA,QAAQ,EAAE,CAAC;AAAEe,MAAAA;AAAF,KAAD,KACP7C,WAAW,CAACgB,KAAZ,GAAoB6B,WAAW,CAACC,MAAZ,CAAmBa;AAF5C,IANF,CADF;AAeA,QAAMI,iBAAiB,GAAGtF,aAA1B;AACA,QAAMuF,kBAAkB,GAAGtF,cAA3B;;AAEA,QAAMuF,aAAa,GACjBC,KADoB,IAEjB;AACH;;AACA,UAAM;AAAEjC,MAAAA;AAAF,QAAgBiC,KAAtB;AACAvE,IAAAA,QAAQ,CAACqB,KAAT,GAAiBkD,KAAK,CAACC,YAAvB;AAEApE,IAAAA,UAAU,CAACiB,KAAX,GAAmBnB,QAAQ,CAACmB,KAAT,GAAiBhB,WAAW,CAACgB,KAAhD;AAEA,UAAMvC,aAAa,GAAGsF,iBAAH,aAAGA,iBAAH,cAAGA,iBAAH,GAAwBjE,SAAS,CAACkB,KAAV,GAAkB,CAA7D;AACA,UAAMtC,cAAc,GAAGsF,kBAAH,aAAGA,kBAAH,cAAGA,kBAAH,GAAyBjE,UAAU,CAACiB,KAAX,GAAmB,CAAhE;AAEA,UAAMoD,YAAY,GAAGrD,sBAAsB,KAAKpB,QAAQ,CAACqB,KAAT,GAAiBL,QAAjE;AACA,UAAMwD,YAAY,GAAG,CAACxE,QAAQ,CAACqB,KAAT,GAAiB3C,SAAS,GAAG4D,SAA9B,IAA2CtB,QAAhE;AAEA,QAAIgB,OAAO,GAAG,CAAd;;AAEA,QAAIjC,QAAQ,CAACsB,KAAT,KAAmB,CAAvB,EAA0B;AACxB,UAAImD,YAAY,GAAG1F,aAAnB,EAAkC;AAChCkD,QAAAA,OAAO,GAAG7B,SAAS,CAACkB,KAApB;AACD,OAFD,MAEO,IAAImD,YAAY,GAAG,CAACzF,cAApB,EAAoC;AACzCiD,QAAAA,OAAO,GAAG,CAAC5B,UAAU,CAACiB,KAAtB;AACD;AACF,KAND,MAMO,IAAItB,QAAQ,CAACsB,KAAT,KAAmB,CAAvB,EAA0B;AAC/B;AACA,UAAImD,YAAY,GAAG,CAAC1F,aAApB,EAAmC;AACjCkD,QAAAA,OAAO,GAAG7B,SAAS,CAACkB,KAApB;AACD;AACF,KALM,MAKA;AACL;AACA,UAAImD,YAAY,GAAGzF,cAAnB,EAAmC;AACjCiD,QAAAA,OAAO,GAAG,CAAC5B,UAAU,CAACiB,KAAtB;AACD;AACF;;AAEDgB,IAAAA,UAAU,CAACoC,YAAD,EAAezC,OAAf,EAAwBM,SAAS,GAAGtB,QAApC,CAAV;AACD,GApCD;;AAsCA,QAAML,KAAK,GAAG,MAAM;AAClB;;AACA0B,IAAAA,UAAU,CAACjB,sBAAsB,EAAvB,EAA2B,CAA3B,CAAV;AACD,GAHD;;AAKA,QAAMsD,UAAU,GAAG5G,OAAO,CAAC6G,GAAR,GAAcC,OAAd,CAAsB,MAAM;AAC7C,QAAI7E,QAAQ,CAACsB,KAAT,KAAmB,CAAvB,EAA0B;AACxBV,MAAAA,KAAK;AACN;AACF,GAJkB,CAAnB;AAMA,QAAMkE,UAAU,GAAG/G,OAAO,CAACgH,GAAR,GAChBC,QADgB,CACNR,KAAD,IAA8D;AACtEvE,IAAAA,QAAQ,CAACqB,KAAT,GAAiBkD,KAAK,CAACC,YAAvB;AAEA,UAAMQ,SAAS,GACbjF,QAAQ,CAACsB,KAAT,KAAmB,CAAC,CAApB,GACI,OADJ,GAEItB,QAAQ,CAACsB,KAAT,KAAmB,CAAnB,GACA,MADA,GAEAkD,KAAK,CAACC,YAAN,GAAqB,CAArB,GACA,MADA,GAEA,OAPN;;AASA,QAAIzE,QAAQ,CAACsB,KAAT,KAAmB,CAAnB,IAAwBrC,wBAA5B,EAAsD;AACpDb,MAAAA,OAAO,CAACa,wBAAD,CAAP,CAAkCgG,SAAlC;AACD,KAFD,MAEO,IAAIjF,QAAQ,CAACsB,KAAT,KAAmB,CAAnB,IAAwBpC,yBAA5B,EAAuD;AAC5Dd,MAAAA,OAAO,CAACc,yBAAD,CAAP,CAAmC+F,SAAnC;AACD;;AACD1D,IAAAA,mBAAmB;AACpB,GAnBgB,EAoBhB2D,KApBgB,CAqBdV,KAAD,IAAmE;AACjED,IAAAA,aAAa,CAACC,KAAD,CAAb;AACD,GAvBc,CAAnB;;AA0BA,MAAIrF,8BAAJ,EAAoC;AAClC2F,IAAAA,UAAU,CAAC3F,8BAAX,CAA0CA,8BAA1C;AACD;;AAED2F,EAAAA,UAAU,CAACK,aAAX,CAAyB,CACvB,CAACzB,uBADsB,EAEvBD,sBAFuB,CAAzB;AAIAkB,EAAAA,UAAU,CAACS,uBAAX,CAAmC,IAAnC;AAEAxH,EAAAA,mBAAmB,CAACkB,GAAD,EAAM,MAAM6B,gBAAgB,CAACyB,OAA7B,EAAsC,CACvDzB,gBADuD,CAAtC,CAAnB;AAIAmE,EAAAA,UAAU,CAAC1F,OAAX,CAAmBA,OAAO,KAAK,KAA/B;AAEA,QAAMiG,aAAa,GAAGhH,gBAAgB,CACpC,OAAO;AACLuF,IAAAA,SAAS,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE3D,kBAAkB,CAACoB;AAAjC,KAAD,CADN;AAELgE,IAAAA,aAAa,EAAEtF,QAAQ,CAACsB,KAAT,KAAmB,CAAnB,GAAuB,MAAvB,GAAgC;AAF1C,GAAP,CADoC,EAKpC,CAACpB,kBAAD,EAAqBF,QAArB,CALoC,CAAtC;AAQA,QAAMuF,kBAAkB,gBACtB,oBAAC,eAAD;AAAiB,IAAA,OAAO,EAAET,UAA1B;AAAsC,IAAA,WAAW,EAAC;AAAlD,kBACE,oBAAC,QAAD,CAAU,IAAV,eACM/E,cADN;AAEE,IAAA,QAAQ,EAAEmD,WAFZ;AAGE,IAAA,KAAK,EAAE,CAACa,MAAM,CAACyB,SAAR,EAAmBnG,cAAnB;AAHT,MAIGyE,WAJH,EAKGK,YALH,eAME,oBAAC,eAAD;AAAiB,IAAA,OAAO,EAAEQ,UAA1B;AAAsC,IAAA,WAAW,EAAC;AAAlD,kBACE,oBAAC,QAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAE,CAACU,aAAD,EAAgB/F,sBAAhB;AAAtB,KACGgE,QADH,CADF,CANF,CADF,CADF;AAiBA,SAAOxD,MAAM,gBACX,oBAAC,IAAD;AAAM,IAAA,MAAM,EAAEA;AAAd,KAAuByF,kBAAvB,CADW,GAGXA,kBAHF;AAKD,CArbyB,CAA5B;AAwbA,eAAe3G,SAAf;AAGA,MAAMmF,MAAM,GAAGtF,UAAU,CAACgH,MAAX,CAAkB;AAC/BD,EAAAA,SAAS,EAAE;AACTE,IAAAA,QAAQ,EAAE;AADD,GADoB;AAI/B1B,EAAAA,WAAW,EAAE,EACX,GAAGvF,UAAU,CAACkH,kBADH;AAEXC,IAAAA,aAAa,EAAEpH,WAAW,CAACqH,KAAZ,GAAoB,aAApB,GAAoC;AAFxC,GAJkB;AAQ/BzB,EAAAA,YAAY,EAAE,EACZ,GAAG3F,UAAU,CAACkH,kBADF;AAEZC,IAAAA,aAAa,EAAEpH,WAAW,CAACqH,KAAZ,GAAoB,KAApB,GAA4B;AAF/B;AARiB,CAAlB,CAAf", "sourcesContent": ["// Similarily to the DrawerLayout component this deserves to be put in a\n// separate repo. Although, keeping it here for the time being will allow us to\n// move faster and fix possible issues quicker\n\nimport React, {\n  ForwardedRef,\n  forwardRef,\n  useCallback,\n  useImperativeHandle,\n  useRef,\n} from 'react';\nimport { GestureObjects as Gesture } from '../handlers/gestures/gestureObjects';\nimport { GestureDetector } from '../handlers/gestures/GestureDetector';\nimport {\n  GestureStateChangeEvent,\n  GestureUpdateEvent,\n} from '../handlers/gestureHandlerCommon';\nimport type { PanGestureHandlerProps } from '../handlers/PanGestureHandler';\nimport type { PanGestureHandlerEventPayload } from '../handlers/GestureHandlerEventPayload';\nimport Animated, {\n  Extrapolation,\n  SharedValue,\n  interpolate,\n  runOnJS,\n  useAnimatedStyle,\n  useSharedValue,\n  withSpring,\n} from 'react-native-reanimated';\nimport {\n  I18nManager,\n  LayoutChangeEvent,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nconst DRAG_TOSS = 0.05;\n\ntype SwipeableExcludes = Exclude<\n  keyof PanGestureHandlerProps,\n  'onGestureEvent' | 'onHandlerStateChange'\n>;\n\nexport interface SwipeableProps\n  extends Pick<PanGestureHandlerProps, SwipeableExcludes> {\n  /**\n   * Enables two-finger gestures on supported devices, for example iPads with\n   * trackpads. If not enabled the gesture will require click + drag, with\n   * `enableTrackpadTwoFingerGesture` swiping with two fingers will also trigger\n   * the gesture.\n   */\n  enableTrackpadTwoFingerGesture?: boolean;\n\n  /**\n   * Specifies how much the visual interaction will be delayed compared to the\n   * gesture distance. e.g. value of 1 will indicate that the swipeable panel\n   * should exactly follow the gesture, 2 means it is going to be two times\n   * \"slower\".\n   */\n  friction?: number;\n\n  /**\n   * Distance from the left edge at which released panel will animate to the\n   * open state (or the open panel will animate into the closed state). By\n   * default it's a half of the panel's width.\n   */\n  leftThreshold?: number;\n\n  /**\n   * Distance from the right edge at which released panel will animate to the\n   * open state (or the open panel will animate into the closed state). By\n   * default it's a half of the panel's width.\n   */\n  rightThreshold?: number;\n\n  /**\n   * Distance that the panel must be dragged from the left edge to be considered\n   * a swipe. The default value is 10.\n   */\n  dragOffsetFromLeftEdge?: number;\n\n  /**\n   * Distance that the panel must be dragged from the right edge to be considered\n   * a swipe. The default value is 10.\n   */\n  dragOffsetFromRightEdge?: number;\n\n  /**\n   * Value indicating if the swipeable panel can be pulled further than the left\n   * actions panel's width. It is set to true by default as long as the left\n   * panel render method is present.\n   */\n  overshootLeft?: boolean;\n\n  /**\n   * Value indicating if the swipeable panel can be pulled further than the\n   * right actions panel's width. It is set to true by default as long as the\n   * right panel render method is present.\n   */\n  overshootRight?: boolean;\n\n  /**\n   * Specifies how much the visual interaction will be delayed compared to the\n   * gesture distance at overshoot. Default value is 1, it mean no friction, for\n   * a native feel, try 8 or above.\n   */\n  overshootFriction?: number;\n\n  /**\n   * Called when action panel gets open (either right or left).\n   */\n  onSwipeableOpen?: (\n    direction: 'left' | 'right',\n    swipeable: SwipeableMethods\n  ) => void;\n\n  /**\n   * Called when action panel is closed.\n   */\n  onSwipeableClose?: (\n    direction: 'left' | 'right',\n    swipeable: SwipeableMethods\n  ) => void;\n\n  /**\n   * Called when action panel starts animating on open (either right or left).\n   */\n  onSwipeableWillOpen?: (direction: 'left' | 'right') => void;\n\n  /**\n   * Called when action panel starts animating on close.\n   */\n  onSwipeableWillClose?: (direction: 'left' | 'right') => void;\n\n  /**\n   * Called when action panel starts being shown on dragging to open.\n   */\n  onSwipeableOpenStartDrag?: (direction: 'left' | 'right') => void;\n\n  /**\n   * Called when action panel starts being shown on dragging to close.\n   */\n  onSwipeableCloseStartDrag?: (direction: 'left' | 'right') => void;\n\n  /**\n   *\n   * This map describes the values to use as inputRange for extra interpolation:\n   * AnimatedValue: [startValue, endValue]\n   *\n   * progressAnimatedValue: [0, 1] dragAnimatedValue: [0, +]\n   *\n   * To support `rtl` flexbox layouts use `flexDirection` styling.\n   * */\n  renderLeftActions?: (\n    progressAnimatedValue: SharedValue<number>,\n    dragAnimatedValue: SharedValue<number>,\n    swipeable: SwipeableMethods\n  ) => React.ReactNode;\n  /**\n   *\n   * This map describes the values to use as inputRange for extra interpolation:\n   * AnimatedValue: [startValue, endValue]\n   *\n   * progressAnimatedValue: [0, 1] dragAnimatedValue: [0, -]\n   *\n   * To support `rtl` flexbox layouts use `flexDirection` styling.\n   * */\n  renderRightActions?: (\n    progressAnimatedValue: SharedValue<number>,\n    dragAnimatedValue: SharedValue<number>,\n    swipeable: SwipeableMethods\n  ) => React.ReactNode;\n\n  animationOptions?: Record<string, unknown>;\n\n  /**\n   * Style object for the container (`Animated.View`), for example to override\n   * `overflow: 'hidden'`.\n   */\n  containerStyle?: StyleProp<ViewStyle>;\n\n  /**\n   * Style object for the children container (`Animated.View`), for example to\n   * apply `flex: 1`\n   */\n  childrenContainerStyle?: StyleProp<ViewStyle>;\n}\n\nexport interface SwipeableMethods {\n  close: () => void;\n  openLeft: () => void;\n  openRight: () => void;\n  reset: () => void;\n}\n\nconst Swipeable = forwardRef<SwipeableMethods, SwipeableProps>(\n  function Swipeable(\n    props: SwipeableProps,\n    ref: ForwardedRef<SwipeableMethods>\n  ) {\n    const {\n      leftThreshold,\n      rightThreshold,\n      onSwipeableOpenStartDrag,\n      onSwipeableCloseStartDrag,\n      enableTrackpadTwoFingerGesture,\n      enabled,\n      containerStyle,\n      childrenContainerStyle,\n      animationOptions,\n      overshootLeft,\n      overshootRight,\n      onSwipeableWillOpen,\n      onSwipeableWillClose,\n      onSwipeableOpen,\n      onSwipeableClose,\n      testID,\n      ...remainingProps\n    } = props;\n\n    const rowState = useSharedValue<number>(0);\n\n    const userDrag = useSharedValue<number>(0);\n    const appliedTranslation = useSharedValue<number>(0);\n\n    const rowWidth = useSharedValue<number>(0);\n    const leftWidth = useSharedValue<number>(0);\n    const rightWidth = useSharedValue<number>(0);\n    const rightOffset = useSharedValue<number>(0);\n\n    const leftActionTranslate = useSharedValue<number>(0);\n    const rightActionTranslate = useSharedValue<number>(0);\n\n    const showLeftProgress = useSharedValue<number>(0);\n    const showRightProgress = useSharedValue<number>(0);\n\n    const swipeableMethods = useRef<SwipeableMethods>({\n      close: () => {\n        'worklet';\n      },\n      openLeft: () => {\n        'worklet';\n      },\n      openRight: () => {\n        'worklet';\n      },\n      reset: () => {\n        'worklet';\n      },\n    });\n\n    const defaultProps = {\n      friction: 1,\n      overshootFriction: 1,\n    };\n\n    const {\n      friction = defaultProps.friction,\n      overshootFriction = defaultProps.overshootFriction,\n    } = props;\n\n    const overshootLeftProp = overshootLeft;\n    const overshootRightProp = overshootRight;\n\n    const calculateCurrentOffset = useCallback(() => {\n      'worklet';\n      if (rowState.value === 1) {\n        return leftWidth.value;\n      } else if (rowState.value === -1) {\n        return -rowWidth.value - rightOffset.value;\n      }\n      return 0;\n    }, [leftWidth, rightOffset, rowState, rowWidth]);\n\n    const updateAnimatedEvent = () => {\n      'worklet';\n      rightWidth.value = Math.max(0, rowWidth.value - rightOffset.value);\n\n      const overshootLeft = overshootLeftProp ?? leftWidth.value > 0;\n      const overshootRight = overshootRightProp ?? rightWidth.value > 0;\n\n      const startOffset =\n        rowState.value === 1\n          ? leftWidth.value\n          : rowState.value === -1\n          ? -rightWidth.value\n          : 0;\n\n      const offsetDrag = userDrag.value / friction + startOffset;\n\n      appliedTranslation.value = interpolate(\n        offsetDrag,\n        [\n          -rightWidth.value - 1,\n          -rightWidth.value,\n          leftWidth.value,\n          leftWidth.value + 1,\n        ],\n        [\n          -rightWidth.value - (overshootRight ? 1 / overshootFriction : 0),\n          -rightWidth.value,\n          leftWidth.value,\n          leftWidth.value + (overshootLeft ? 1 / overshootFriction : 0),\n        ]\n      );\n\n      showLeftProgress.value =\n        leftWidth.value > 0\n          ? interpolate(\n              appliedTranslation.value,\n              [-1, 0, leftWidth.value],\n              [0, 0, 1]\n            )\n          : 0;\n      leftActionTranslate.value = interpolate(\n        showLeftProgress.value,\n        [0, Number.MIN_VALUE],\n        [-10000, 0],\n        Extrapolation.CLAMP\n      );\n      showRightProgress.value =\n        rightWidth.value > 0\n          ? interpolate(\n              appliedTranslation.value,\n              [-rightWidth.value, 0, 1],\n              [1, 0, 0]\n            )\n          : 0;\n      rightActionTranslate.value = interpolate(\n        showRightProgress.value,\n        [0, Number.MIN_VALUE],\n        [-10000, 0],\n        Extrapolation.CLAMP\n      );\n    };\n\n    const dispatchImmediateEvents = useCallback(\n      (fromValue: number, toValue: number) => {\n        if (toValue > 0 && onSwipeableWillOpen) {\n          onSwipeableWillOpen('left');\n        } else if (toValue < 0 && onSwipeableWillOpen) {\n          onSwipeableWillOpen('right');\n        } else if (onSwipeableWillClose) {\n          const closingDirection = fromValue > 0 ? 'left' : 'right';\n          onSwipeableWillClose(closingDirection);\n        }\n      },\n      [onSwipeableWillClose, onSwipeableWillOpen]\n    );\n\n    const dispatchEndEvents = useCallback(\n      (fromValue: number, toValue: number) => {\n        if (toValue > 0 && onSwipeableOpen) {\n          onSwipeableOpen('left', swipeableMethods.current);\n        } else if (toValue < 0 && onSwipeableOpen) {\n          onSwipeableOpen('right', swipeableMethods.current);\n        } else if (onSwipeableClose) {\n          const closingDirection = fromValue > 0 ? 'left' : 'right';\n          onSwipeableClose(closingDirection, swipeableMethods.current);\n        }\n      },\n      [onSwipeableClose, onSwipeableOpen]\n    );\n\n    const animationOptionsProp = animationOptions;\n\n    const animateRow = useCallback(\n      (fromValue: number, toValue: number, velocityX?: number) => {\n        'worklet';\n        rowState.value = Math.sign(toValue);\n\n        const translationSpringConfig = {\n          duration: 1000,\n          dampingRatio: 0.9,\n          stiffness: 500,\n          velocity: velocityX,\n          overshootClamping: true,\n          ...animationOptionsProp,\n        };\n\n        const progressSpringConfig = {\n          ...translationSpringConfig,\n          velocity: 0,\n        };\n\n        appliedTranslation.value = withSpring(\n          toValue,\n          translationSpringConfig,\n          (isFinished) => {\n            if (isFinished) {\n              runOnJS(dispatchEndEvents)(fromValue, toValue);\n            }\n          }\n        );\n\n        const progressTarget = toValue === 0 ? 0 : 1;\n\n        showLeftProgress.value =\n          leftWidth.value > 0\n            ? withSpring(progressTarget, progressSpringConfig)\n            : 0;\n        showRightProgress.value =\n          rightWidth.value > 0\n            ? withSpring(progressTarget, progressSpringConfig)\n            : 0;\n\n        runOnJS(dispatchImmediateEvents)(fromValue, toValue);\n      },\n      [\n        rowState,\n        animationOptionsProp,\n        appliedTranslation,\n        showLeftProgress,\n        leftWidth.value,\n        showRightProgress,\n        rightWidth.value,\n        dispatchImmediateEvents,\n        dispatchEndEvents,\n      ]\n    );\n\n    const onRowLayout = ({ nativeEvent }: LayoutChangeEvent) => {\n      rowWidth.value = nativeEvent.layout.width;\n    };\n\n    const {\n      children,\n      renderLeftActions,\n      renderRightActions,\n      dragOffsetFromLeftEdge = 10,\n      dragOffsetFromRightEdge = 10,\n    } = props;\n\n    swipeableMethods.current = {\n      close() {\n        'worklet';\n        animateRow(calculateCurrentOffset(), 0);\n      },\n      openLeft() {\n        'worklet';\n        animateRow(calculateCurrentOffset(), leftWidth.value);\n      },\n      openRight() {\n        'worklet';\n        rightWidth.value = rowWidth.value - rightOffset.value;\n        animateRow(calculateCurrentOffset(), -rightWidth.value);\n      },\n      reset() {\n        'worklet';\n        userDrag.value = 0;\n        showLeftProgress.value = 0;\n        appliedTranslation.value = 0;\n        rowState.value = 0;\n      },\n    };\n\n    const leftAnimatedStyle = useAnimatedStyle(\n      () => ({\n        transform: [\n          {\n            translateX: leftActionTranslate.value,\n          },\n        ],\n      }),\n      [leftActionTranslate]\n    );\n\n    const leftElement = renderLeftActions && (\n      <Animated.View style={[styles.leftActions, leftAnimatedStyle]}>\n        {renderLeftActions(\n          showLeftProgress,\n          appliedTranslation,\n          swipeableMethods.current\n        )}\n        <View\n          onLayout={({ nativeEvent }) =>\n            (leftWidth.value = nativeEvent.layout.x)\n          }\n        />\n      </Animated.View>\n    );\n\n    const rightAnimatedStyle = useAnimatedStyle(\n      () => ({\n        transform: [\n          {\n            translateX: rightActionTranslate.value,\n          },\n        ],\n      }),\n      [rightActionTranslate]\n    );\n\n    const rightElement = renderRightActions && (\n      <Animated.View style={[styles.rightActions, rightAnimatedStyle]}>\n        {renderRightActions(\n          showRightProgress,\n          appliedTranslation,\n          swipeableMethods.current\n        )}\n        <View\n          onLayout={({ nativeEvent }) =>\n            (rightOffset.value = nativeEvent.layout.x)\n          }\n        />\n      </Animated.View>\n    );\n\n    const leftThresholdProp = leftThreshold;\n    const rightThresholdProp = rightThreshold;\n\n    const handleRelease = (\n      event: GestureStateChangeEvent<PanGestureHandlerEventPayload>\n    ) => {\n      'worklet';\n      const { velocityX } = event;\n      userDrag.value = event.translationX;\n\n      rightWidth.value = rowWidth.value - rightOffset.value;\n\n      const leftThreshold = leftThresholdProp ?? leftWidth.value / 2;\n      const rightThreshold = rightThresholdProp ?? rightWidth.value / 2;\n\n      const startOffsetX = calculateCurrentOffset() + userDrag.value / friction;\n      const translationX = (userDrag.value + DRAG_TOSS * velocityX) / friction;\n\n      let toValue = 0;\n\n      if (rowState.value === 0) {\n        if (translationX > leftThreshold) {\n          toValue = leftWidth.value;\n        } else if (translationX < -rightThreshold) {\n          toValue = -rightWidth.value;\n        }\n      } else if (rowState.value === 1) {\n        // Swiped to left\n        if (translationX > -leftThreshold) {\n          toValue = leftWidth.value;\n        }\n      } else {\n        // Swiped to right\n        if (translationX < rightThreshold) {\n          toValue = -rightWidth.value;\n        }\n      }\n\n      animateRow(startOffsetX, toValue, velocityX / friction);\n    };\n\n    const close = () => {\n      'worklet';\n      animateRow(calculateCurrentOffset(), 0);\n    };\n\n    const tapGesture = Gesture.Tap().onStart(() => {\n      if (rowState.value !== 0) {\n        close();\n      }\n    });\n\n    const panGesture = Gesture.Pan()\n      .onUpdate((event: GestureUpdateEvent<PanGestureHandlerEventPayload>) => {\n        userDrag.value = event.translationX;\n\n        const direction =\n          rowState.value === -1\n            ? 'right'\n            : rowState.value === 1\n            ? 'left'\n            : event.translationX > 0\n            ? 'left'\n            : 'right';\n\n        if (rowState.value === 0 && onSwipeableOpenStartDrag) {\n          runOnJS(onSwipeableOpenStartDrag)(direction);\n        } else if (rowState.value !== 0 && onSwipeableCloseStartDrag) {\n          runOnJS(onSwipeableCloseStartDrag)(direction);\n        }\n        updateAnimatedEvent();\n      })\n      .onEnd(\n        (event: GestureStateChangeEvent<PanGestureHandlerEventPayload>) => {\n          handleRelease(event);\n        }\n      );\n\n    if (enableTrackpadTwoFingerGesture) {\n      panGesture.enableTrackpadTwoFingerGesture(enableTrackpadTwoFingerGesture);\n    }\n\n    panGesture.activeOffsetX([\n      -dragOffsetFromRightEdge,\n      dragOffsetFromLeftEdge,\n    ]);\n    tapGesture.shouldCancelWhenOutside(true);\n\n    useImperativeHandle(ref, () => swipeableMethods.current, [\n      swipeableMethods,\n    ]);\n\n    panGesture.enabled(enabled !== false);\n\n    const animatedStyle = useAnimatedStyle(\n      () => ({\n        transform: [{ translateX: appliedTranslation.value }],\n        pointerEvents: rowState.value === 0 ? 'auto' : 'box-only',\n      }),\n      [appliedTranslation, rowState]\n    );\n\n    const swipeableComponent = (\n      <GestureDetector gesture={panGesture} touchAction=\"pan-y\">\n        <Animated.View\n          {...remainingProps}\n          onLayout={onRowLayout}\n          style={[styles.container, containerStyle]}>\n          {leftElement}\n          {rightElement}\n          <GestureDetector gesture={tapGesture} touchAction=\"pan-y\">\n            <Animated.View style={[animatedStyle, childrenContainerStyle]}>\n              {children}\n            </Animated.View>\n          </GestureDetector>\n        </Animated.View>\n      </GestureDetector>\n    );\n\n    return testID ? (\n      <View testID={testID}>{swipeableComponent}</View>\n    ) : (\n      swipeableComponent\n    );\n  }\n);\n\nexport default Swipeable;\nexport type SwipeableRef = ForwardedRef<SwipeableMethods>;\n\nconst styles = StyleSheet.create({\n  container: {\n    overflow: 'hidden',\n  },\n  leftActions: {\n    ...StyleSheet.absoluteFillObject,\n    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',\n  },\n  rightActions: {\n    ...StyleSheet.absoluteFillObject,\n    flexDirection: I18nManager.isRTL ? 'row' : 'row-reverse',\n  },\n});\n"]}