{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/diag/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AA+CH;;;;GAIG;AACH,MAAM,CAAN,IAAY,YAwBX;AAxBD,WAAY,YAAY;IACtB,uFAAuF;IACvF,+CAAQ,CAAA;IAER,mCAAmC;IACnC,kDAAU,CAAA;IAEV,oCAAoC;IACpC,gDAAS,CAAA;IAET,wCAAwC;IACxC,gDAAS,CAAA;IAET,gCAAgC;IAChC,kDAAU,CAAA;IAEV;;;OAGG;IACH,sDAAY,CAAA;IAEZ,2DAA2D;IAC3D,gDAAU,CAAA;AACZ,CAAC,EAxBW,YAAY,KAAZ,YAAY,QAwBvB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type DiagLogFunction = (message: string, ...args: unknown[]) => void;\n\n/**\n * Defines an internal diagnostic logger interface which is used to log internal diagnostic\n * messages, you can set the default diagnostic logger via the {@link DiagAPI} setLogger function.\n * API provided implementations include :-\n * - a No-Op {@link createNoopDiagLogger}\n * - a {@link DiagLogLevel} filtering wrapper {@link createLogLevelDiagLogger}\n * - a general Console {@link DiagConsoleLogger} version.\n */\nexport interface DiagLogger {\n  /** Log an error scenario that was not expected and caused the requested operation to fail. */\n  error: DiagLogFunction;\n\n  /**\n   * Log a warning scenario to inform the developer of an issues that should be investigated.\n   * The requested operation may or may not have succeeded or completed.\n   */\n  warn: DiagLogFunction;\n\n  /**\n   * Log a general informational message, this should not affect functionality.\n   * This is also the default logging level so this should NOT be used for logging\n   * debugging level information.\n   */\n  info: DiagLogFunction;\n\n  /**\n   * Log a general debug message that can be useful for identifying a failure.\n   * Information logged at this level may include diagnostic details that would\n   * help identify a failure scenario.\n   * For example: Logging the order of execution of async operations.\n   */\n  debug: DiagLogFunction;\n\n  /**\n   * Log a detailed (verbose) trace level logging that can be used to identify failures\n   * where debug level logging would be insufficient, this level of tracing can include\n   * input and output parameters and as such may include PII information passing through\n   * the API. As such it is recommended that this level of tracing should not be enabled\n   * in a production environment.\n   */\n  verbose: DiagLogFunction;\n}\n\n/**\n * Defines the available internal logging levels for the diagnostic logger, the numeric values\n * of the levels are defined to match the original values from the initial LogLevel to avoid\n * compatibility/migration issues for any implementation that assume the numeric ordering.\n */\nexport enum DiagLogLevel {\n  /** Diagnostic Logging level setting to disable all logging (except and forced logs) */\n  NONE = 0,\n\n  /** Identifies an error scenario */\n  ERROR = 30,\n\n  /** Identifies a warning scenario */\n  WARN = 50,\n\n  /** General informational log message */\n  INFO = 60,\n\n  /** General debug log message */\n  DEBUG = 70,\n\n  /**\n   * Detailed trace level logging should only be used for development, should only be set\n   * in a development environment.\n   */\n  VERBOSE = 80,\n\n  /** Used to set the logging level to include all logging */\n  ALL = 9999,\n}\n\n/**\n * Defines options for ComponentLogger\n */\nexport interface ComponentLoggerOptions {\n  namespace: string;\n}\n\nexport interface DiagLoggerOptions {\n  /**\n   * The {@link DiagLogLevel} used to filter logs sent to the logger.\n   *\n   * @defaultValue DiagLogLevel.INFO\n   */\n  logLevel?: DiagLogLevel;\n\n  /**\n   * Setting this value to `true` will suppress the warning message normally emitted when registering a logger when another logger is already registered.\n   */\n  suppressOverrideMessage?: boolean;\n}\n\nexport interface DiagLoggerApi {\n  /**\n   * Set the global DiagLogger and DiagLogLevel.\n   * If a global diag logger is already set, this will override it.\n   *\n   * @param logger - The {@link DiagLogger} instance to set as the default logger.\n   * @param options - A {@link DiagLoggerOptions} object. If not provided, default values will be set.\n   * @returns `true` if the logger was successfully registered, else `false`\n   */\n  setLogger(logger: DiagLogger, options?: DiagLoggerOptions): boolean;\n\n  /**\n   *\n   * @param logger - The {@link DiagLogger} instance to set as the default logger.\n   * @param logLevel - The {@link DiagLogLevel} used to filter logs sent to the logger. If not provided it will default to {@link DiagLogLevel.INFO}.\n   * @returns `true` if the logger was successfully registered, else `false`\n   */\n  setLogger(logger: DiagLogger, logLevel?: DiagLogLevel): boolean;\n}\n"]}