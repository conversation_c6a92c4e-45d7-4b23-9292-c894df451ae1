{"version": 3, "sources": ["NativeRNGestureHandlerModule.ts"], "names": ["TurboModuleRegistry", "getEnforcing"], "mappings": "AAAA,SAASA,mBAAT,QAAiD,cAAjD;AAyBA,eAAeA,mBAAmB,CAACC,YAApB,CAAuC,wBAAvC,CAAf", "sourcesContent": ["import { TurboModuleRegistry, TurboModule } from 'react-native';\nimport { Double } from 'react-native/Libraries/Types/CodegenTypes';\n\nexport interface Spec extends TurboModule {\n  handleSetJSResponder: (tag: Double, blockNativeResponder: boolean) => void;\n  handleClearJSResponder: () => void;\n  createGestureHandler: (\n    handlerName: string,\n    handlerTag: Double,\n    // Record<> is not supported by codegen\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    config: Object\n  ) => void;\n  attachGestureHandler: (\n    handlerTag: Double,\n    newView: Double,\n    actionType: Double\n  ) => void;\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  updateGestureHandler: (handlerTag: Double, newConfig: Object) => void;\n  dropGestureHandler: (handlerTag: Double) => void;\n  install: () => boolean;\n  flushOperations: () => void;\n}\n\nexport default TurboModuleRegistry.getEnforcing<Spec>('RNGestureHandlerModule');\n"]}