{"version": 3, "sources": ["GestureHandlerWebDelegate.ts"], "names": ["findNodeHandle", "PointerEventManager", "State", "isPointerInBounds", "MouseB<PERSON>on", "KeyboardEventManager", "GestureHandlerWebDelegate", "userSelect", "touchAction", "get<PERSON>iew", "view", "init", "viewRef", "handler", "Error", "getTag", "isInitialized", "<PERSON><PERSON><PERSON><PERSON>", "defaultViewStyles", "style", "config", "getConfig", "setUserSelect", "enabled", "setTouchAction", "setContextMenu", "eventManagers", "push", "for<PERSON>ach", "manager", "attachEventManager", "x", "y", "measure<PERSON>iew", "rect", "getBoundingClientRect", "pageX", "left", "pageY", "top", "width", "height", "reset", "resetManager", "tryResetCursor", "activeCursor", "getState", "ACTIVE", "cursor", "shouldDisableContextMenu", "enableContextMenu", "undefined", "isButtonInConfig", "RIGHT", "addContextMenuListeners", "addEventListener", "disableContextMenu", "removeContextMenuListeners", "removeEventListener", "e", "preventDefault", "stopPropagation", "isHandlerEnabled", "onEnabledChange", "onBegin", "onActivate", "onEnd", "onCancel", "onFail", "destroy", "unregisterListeners"], "mappings": ";;AAAA,SAASA,cAAT,QAA+B,cAA/B;AAMA,OAAOC,mBAAP,MAAgC,uBAAhC;AACA,SAASC,KAAT,QAAsB,aAAtB;AACA,SAASC,iBAAT,QAAkC,UAAlC;AAGA,SAASC,WAAT,QAA4B,qCAA5B;AACA,OAAOC,oBAAP,MAAiC,wBAAjC;AAOA,OAAO,MAAMC,yBAAN,CAEP;AAAA;AAAA,2CAC0B,KAD1B;;AAAA;;AAAA;;AAAA,2CAImD,EAJnD;;AAAA,+CAKiD;AAC7CC,MAAAA,UAAU,EAAE,EADiC;AAE7CC,MAAAA,WAAW,EAAE;AAFgC,KALjD;AAAA;;AAUEC,EAAAA,OAAO,GAAgB;AACrB,WAAO,KAAKC,IAAZ;AACD;;AAEDC,EAAAA,IAAI,CAACC,OAAD,EAAkBC,OAAlB,EAAkD;AACpD,QAAI,CAACD,OAAL,EAAc;AACZ,YAAM,IAAIE,KAAJ,CACH,wCAAuCD,OAAO,CAACE,MAAR,EAAiB,EADrD,CAAN;AAGD;;AAED,SAAKC,aAAL,GAAqB,IAArB;AAEA,SAAKC,cAAL,GAAsBJ,OAAtB;AACA,SAAKH,IAAL,GAAYV,cAAc,CAACY,OAAD,CAA1B;AAEA,SAAKM,iBAAL,GAAyB;AACvBX,MAAAA,UAAU,EAAE,KAAKG,IAAL,CAAUS,KAAV,CAAgBZ,UADL;AAEvBC,MAAAA,WAAW,EAAE,KAAKE,IAAL,CAAUS,KAAV,CAAgBX;AAFN,KAAzB;AAKA,UAAMY,MAAM,GAAGP,OAAO,CAACQ,SAAR,EAAf;AAEA,SAAKC,aAAL,CAAmBF,MAAM,CAACG,OAA1B;AACA,SAAKC,cAAL,CAAoBJ,MAAM,CAACG,OAA3B;AACA,SAAKE,cAAL,CAAoBL,MAAM,CAACG,OAA3B;AAEA,SAAKG,aAAL,CAAmBC,IAAnB,CAAwB,IAAI1B,mBAAJ,CAAwB,KAAKS,IAA7B,CAAxB;AACA,SAAKgB,aAAL,CAAmBC,IAAnB,CAAwB,IAAItB,oBAAJ,CAAyB,KAAKK,IAA9B,CAAxB;AAEA,SAAKgB,aAAL,CAAmBE,OAAnB,CAA4BC,OAAD,IACzB,KAAKZ,cAAL,CAAoBa,kBAApB,CAAuCD,OAAvC,CADF;AAGD;;AAED1B,EAAAA,iBAAiB,CAAC;AAAE4B,IAAAA,CAAF;AAAKC,IAAAA;AAAL,GAAD,EAA8C;AAC7D,WAAO7B,iBAAiB,CAAC,KAAKO,IAAN,EAAY;AAAEqB,MAAAA,CAAF;AAAKC,MAAAA;AAAL,KAAZ,CAAxB;AACD;;AAEDC,EAAAA,WAAW,GAAkB;AAC3B,UAAMC,IAAI,GAAG,KAAKxB,IAAL,CAAUyB,qBAAV,EAAb;AAEA,WAAO;AACLC,MAAAA,KAAK,EAAEF,IAAI,CAACG,IADP;AAELC,MAAAA,KAAK,EAAEJ,IAAI,CAACK,GAFP;AAGLC,MAAAA,KAAK,EAAEN,IAAI,CAACM,KAHP;AAILC,MAAAA,MAAM,EAAEP,IAAI,CAACO;AAJR,KAAP;AAMD;;AAEDC,EAAAA,KAAK,GAAS;AACZ,SAAKhB,aAAL,CAAmBE,OAAnB,CAA4BC,OAAD,IACzBA,OAAO,CAACc,YAAR,EADF;AAGD;;AAEDC,EAAAA,cAAc,GAAG;AACf,UAAMxB,MAAM,GAAG,KAAKH,cAAL,CAAoBI,SAApB,EAAf;;AAEA,QACED,MAAM,CAACyB,YAAP,IACAzB,MAAM,CAACyB,YAAP,KAAwB,MADxB,IAEA,KAAK5B,cAAL,CAAoB6B,QAApB,OAAmC5C,KAAK,CAAC6C,MAH3C,EAIE;AACA,WAAKrC,IAAL,CAAUS,KAAV,CAAgB6B,MAAhB,GAAyB,MAAzB;AACD;AACF;;AAEOC,EAAAA,wBAAwB,CAAC7B,MAAD,EAAiB;AAC/C,WACGA,MAAM,CAAC8B,iBAAP,KAA6BC,SAA7B,IACC,KAAKlC,cAAL,CAAoBmC,gBAApB,CAAqChD,WAAW,CAACiD,KAAjD,CADF,IAEAjC,MAAM,CAAC8B,iBAAP,KAA6B,KAH/B;AAKD;;AAEOI,EAAAA,uBAAuB,CAAClC,MAAD,EAAuB;AACpD,QAAI,KAAK6B,wBAAL,CAA8B7B,MAA9B,CAAJ,EAA2C;AACzC,WAAKV,IAAL,CAAU6C,gBAAV,CAA2B,aAA3B,EAA0C,KAAKC,kBAA/C;AACD,KAFD,MAEO,IAAIpC,MAAM,CAAC8B,iBAAX,EAA8B;AACnC,WAAKxC,IAAL,CAAU6C,gBAAV,CAA2B,aAA3B,EAA0C,KAAKL,iBAA/C;AACD;AACF;;AAEOO,EAAAA,0BAA0B,CAACrC,MAAD,EAAuB;AACvD,QAAI,KAAK6B,wBAAL,CAA8B7B,MAA9B,CAAJ,EAA2C;AACzC,WAAKV,IAAL,CAAUgD,mBAAV,CAA8B,aAA9B,EAA6C,KAAKF,kBAAlD;AACD,KAFD,MAEO,IAAIpC,MAAM,CAAC8B,iBAAX,EAA8B;AACnC,WAAKxC,IAAL,CAAUgD,mBAAV,CAA8B,aAA9B,EAA6C,KAAKR,iBAAlD;AACD;AACF;;AAEOM,EAAAA,kBAAkB,CAAaG,CAAb,EAAkC;AAC1DA,IAAAA,CAAC,CAACC,cAAF;AACD;;AAEOV,EAAAA,iBAAiB,CAAaS,CAAb,EAAkC;AACzDA,IAAAA,CAAC,CAACE,eAAF;AACD;;AAEOvC,EAAAA,aAAa,CAACwC,gBAAD,EAA4B;AAC/C,UAAM;AAAEvD,MAAAA;AAAF,QAAiB,KAAKU,cAAL,CAAoBI,SAApB,EAAvB;AAEA,SAAKX,IAAL,CAAUS,KAAV,CAAgB,YAAhB,IAAgC2C,gBAAgB,GAC5CvD,UAD4C,aAC5CA,UAD4C,cAC5CA,UAD4C,GAC9B,MAD8B,GAE5C,KAAKW,iBAAL,CAAuBX,UAF3B;AAIA,SAAKG,IAAL,CAAUS,KAAV,CAAgB,kBAAhB,IAAsC2C,gBAAgB,GAClDvD,UADkD,aAClDA,UADkD,cAClDA,UADkD,GACpC,MADoC,GAElD,KAAKW,iBAAL,CAAuBX,UAF3B;AAGD;;AAEOiB,EAAAA,cAAc,CAACsC,gBAAD,EAA4B;AAChD,UAAM;AAAEtD,MAAAA;AAAF,QAAkB,KAAKS,cAAL,CAAoBI,SAApB,EAAxB;AAEA,SAAKX,IAAL,CAAUS,KAAV,CAAgB,aAAhB,IAAiC2C,gBAAgB,GAC7CtD,WAD6C,aAC7CA,WAD6C,cAC7CA,WAD6C,GAC9B,MAD8B,GAE7C,KAAKU,iBAAL,CAAuBV,WAF3B,CAHgD,CAOhD;;AACA,SAAKE,IAAL,CAAUS,KAAV,CAAgB,oBAAhB,IAAwC2C,gBAAgB,GACpDtD,WADoD,aACpDA,WADoD,cACpDA,WADoD,GACrC,MADqC,GAEpD,KAAKU,iBAAL,CAAuBV,WAF3B;AAGD;;AAEOiB,EAAAA,cAAc,CAACqC,gBAAD,EAA4B;AAChD,UAAM1C,MAAM,GAAG,KAAKH,cAAL,CAAoBI,SAApB,EAAf;;AAEA,QAAIyC,gBAAJ,EAAsB;AACpB,WAAKR,uBAAL,CAA6BlC,MAA7B;AACD,KAFD,MAEO;AACL,WAAKqC,0BAAL,CAAgCrC,MAAhC;AACD;AACF;;AAED2C,EAAAA,eAAe,CAACxC,OAAD,EAAyB;AACtC,QAAI,CAAC,KAAKP,aAAV,EAAyB;AACvB;AACD;;AAED,SAAKM,aAAL,CAAmBC,OAAnB;AACA,SAAKC,cAAL,CAAoBD,OAApB;AACA,SAAKE,cAAL,CAAoBF,OAApB;AACD;;AAEDyC,EAAAA,OAAO,GAAS,CACd;AACD;;AAEDC,EAAAA,UAAU,GAAS;AACjB,UAAM7C,MAAM,GAAG,KAAKH,cAAL,CAAoBI,SAApB,EAAf;;AAEA,QACE,CAAC,CAAC,KAAKX,IAAL,CAAUS,KAAV,CAAgB6B,MAAjB,IAA2B,KAAKtC,IAAL,CAAUS,KAAV,CAAgB6B,MAAhB,KAA2B,MAAvD,KACA5B,MAAM,CAACyB,YAFT,EAGE;AACA,WAAKnC,IAAL,CAAUS,KAAV,CAAgB6B,MAAhB,GAAyB5B,MAAM,CAACyB,YAAhC;AACD;AACF;;AAEDqB,EAAAA,KAAK,GAAS;AACZ,SAAKtB,cAAL;AACD;;AAEDuB,EAAAA,QAAQ,GAAS;AACf,SAAKvB,cAAL;AACD;;AAEDwB,EAAAA,MAAM,GAAS;AACb,SAAKxB,cAAL;AACD;;AAEMyB,EAAAA,OAAO,CAACjD,MAAD,EAAuB;AACnC,SAAKqC,0BAAL,CAAgCrC,MAAhC;AAEA,SAAKM,aAAL,CAAmBE,OAAnB,CAA4BC,OAAD,IAAa;AACtCA,MAAAA,OAAO,CAACyC,mBAAR;AACD,KAFD;AAGD;;AA5LH", "sourcesContent": ["import { findNodeHandle } from 'react-native';\nimport type IGestureHandler from '../handlers/IGestureHandler';\nimport {\n  GestureHandlerDelegate,\n  MeasureResult,\n} from './GestureHandlerDelegate';\nimport PointerEventManager from './PointerEventManager';\nimport { State } from '../../State';\nimport { isPointerInBounds } from '../utils';\nimport EventManager from './EventManager';\nimport { Config } from '../interfaces';\nimport { MouseButton } from '../../handlers/gestureHandlerCommon';\nimport KeyboardEventManager from './KeyboardEventManager';\n\ninterface DefaultViewStyles {\n  userSelect: string;\n  touchAction: string;\n}\n\nexport class GestureHandlerWebDelegate\n  implements GestureHandlerDelegate<HTMLElement, IGestureHandler>\n{\n  private isInitialized = false;\n  private view!: HTMLElement;\n  private gestureHandler!: IGestureHandler;\n  private eventManagers: EventManager<unknown>[] = [];\n  private defaultViewStyles: DefaultViewStyles = {\n    userSelect: '',\n    touchAction: '',\n  };\n\n  getView(): HTMLElement {\n    return this.view;\n  }\n\n  init(viewRef: number, handler: IGestureHandler): void {\n    if (!viewRef) {\n      throw new Error(\n        `Cannot find HTML Element for handler ${handler.getTag()}`\n      );\n    }\n\n    this.isInitialized = true;\n\n    this.gestureHandler = handler;\n    this.view = findNodeHandle(viewRef) as unknown as HTMLElement;\n\n    this.defaultViewStyles = {\n      userSelect: this.view.style.userSelect,\n      touchAction: this.view.style.touchAction,\n    };\n\n    const config = handler.getConfig();\n\n    this.setUserSelect(config.enabled);\n    this.setTouchAction(config.enabled);\n    this.setContextMenu(config.enabled);\n\n    this.eventManagers.push(new PointerEventManager(this.view));\n    this.eventManagers.push(new KeyboardEventManager(this.view));\n\n    this.eventManagers.forEach((manager) =>\n      this.gestureHandler.attachEventManager(manager)\n    );\n  }\n\n  isPointerInBounds({ x, y }: { x: number; y: number }): boolean {\n    return isPointerInBounds(this.view, { x, y });\n  }\n\n  measureView(): MeasureResult {\n    const rect = this.view.getBoundingClientRect();\n\n    return {\n      pageX: rect.left,\n      pageY: rect.top,\n      width: rect.width,\n      height: rect.height,\n    };\n  }\n\n  reset(): void {\n    this.eventManagers.forEach((manager: EventManager<unknown>) =>\n      manager.resetManager()\n    );\n  }\n\n  tryResetCursor() {\n    const config = this.gestureHandler.getConfig();\n\n    if (\n      config.activeCursor &&\n      config.activeCursor !== 'auto' &&\n      this.gestureHandler.getState() === State.ACTIVE\n    ) {\n      this.view.style.cursor = 'auto';\n    }\n  }\n\n  private shouldDisableContextMenu(config: Config) {\n    return (\n      (config.enableContextMenu === undefined &&\n        this.gestureHandler.isButtonInConfig(MouseButton.RIGHT)) ||\n      config.enableContextMenu === false\n    );\n  }\n\n  private addContextMenuListeners(config: Config): void {\n    if (this.shouldDisableContextMenu(config)) {\n      this.view.addEventListener('contextmenu', this.disableContextMenu);\n    } else if (config.enableContextMenu) {\n      this.view.addEventListener('contextmenu', this.enableContextMenu);\n    }\n  }\n\n  private removeContextMenuListeners(config: Config): void {\n    if (this.shouldDisableContextMenu(config)) {\n      this.view.removeEventListener('contextmenu', this.disableContextMenu);\n    } else if (config.enableContextMenu) {\n      this.view.removeEventListener('contextmenu', this.enableContextMenu);\n    }\n  }\n\n  private disableContextMenu(this: void, e: MouseEvent): void {\n    e.preventDefault();\n  }\n\n  private enableContextMenu(this: void, e: MouseEvent): void {\n    e.stopPropagation();\n  }\n\n  private setUserSelect(isHandlerEnabled: boolean) {\n    const { userSelect } = this.gestureHandler.getConfig();\n\n    this.view.style['userSelect'] = isHandlerEnabled\n      ? userSelect ?? 'none'\n      : this.defaultViewStyles.userSelect;\n\n    this.view.style['webkitUserSelect'] = isHandlerEnabled\n      ? userSelect ?? 'none'\n      : this.defaultViewStyles.userSelect;\n  }\n\n  private setTouchAction(isHandlerEnabled: boolean) {\n    const { touchAction } = this.gestureHandler.getConfig();\n\n    this.view.style['touchAction'] = isHandlerEnabled\n      ? touchAction ?? 'none'\n      : this.defaultViewStyles.touchAction;\n\n    // @ts-ignore This one disables default events on Safari\n    this.view.style['WebkitTouchCallout'] = isHandlerEnabled\n      ? touchAction ?? 'none'\n      : this.defaultViewStyles.touchAction;\n  }\n\n  private setContextMenu(isHandlerEnabled: boolean) {\n    const config = this.gestureHandler.getConfig();\n\n    if (isHandlerEnabled) {\n      this.addContextMenuListeners(config);\n    } else {\n      this.removeContextMenuListeners(config);\n    }\n  }\n\n  onEnabledChange(enabled: boolean): void {\n    if (!this.isInitialized) {\n      return;\n    }\n\n    this.setUserSelect(enabled);\n    this.setTouchAction(enabled);\n    this.setContextMenu(enabled);\n  }\n\n  onBegin(): void {\n    // no-op for now\n  }\n\n  onActivate(): void {\n    const config = this.gestureHandler.getConfig();\n\n    if (\n      (!this.view.style.cursor || this.view.style.cursor === 'auto') &&\n      config.activeCursor\n    ) {\n      this.view.style.cursor = config.activeCursor;\n    }\n  }\n\n  onEnd(): void {\n    this.tryResetCursor();\n  }\n\n  onCancel(): void {\n    this.tryResetCursor();\n  }\n\n  onFail(): void {\n    this.tryResetCursor();\n  }\n\n  public destroy(config: Config): void {\n    this.removeContextMenuListeners(config);\n\n    this.eventManagers.forEach((manager) => {\n      manager.unregisterListeners();\n    });\n  }\n}\n"]}