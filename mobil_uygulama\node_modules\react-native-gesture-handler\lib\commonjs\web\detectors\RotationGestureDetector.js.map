{"version": 3, "sources": ["RotationGestureDetector.ts"], "names": ["RotationGestureDetector", "constructor", "callbacks", "NaN", "onRotationBegin", "onRotation", "onRotationEnd", "updateCurrent", "event", "tracker", "previousTime", "currentTime", "time", "firstPointerID", "secondPointerID", "keyPointers", "firstPointerCoords", "getLastAbsoluteCoords", "secondPointerCoords", "vectorX", "x", "vectorY", "y", "anchorX", "anchorY", "angle", "Math", "atan2", "rotation", "Number", "isNaN", "previousAngle", "PI", "finish", "isInProgress", "set<PERSON>eyPointers", "pointerIDs", "getData", "keys", "next", "value", "onTouchEvent", "eventType", "EventTypes", "DOWN", "ADDITIONAL_POINTER_DOWN", "MOVE", "ADDITIONAL_POINTER_UP", "indexOf", "pointerId", "UP", "getTimeDelta", "getAnchorX", "getAnchorY", "getRotation", "reset"], "mappings": ";;;;;;;AAAA;;;;AASe,MAAMA,uBAAN,CAEf;AAkBEC,EAAAA,WAAW,CAACC,SAAD,EAAqC;AAAA;;AAAA;;AAAA;;AAAA,yCAb1B,CAa0B;;AAAA,0CAZzB,CAYyB;;AAAA,2CAVxB,CAUwB;;AAAA,sCAT7B,CAS6B;;AAAA,qCAP9B,CAO8B;;AAAA,qCAN9B,CAM8B;;AAAA,0CAJzB,KAIyB;;AAAA,yCAFhB,CAACC,GAAD,EAAMA,GAAN,CAEgB;;AAC9C,SAAKC,eAAL,GAAuBF,SAAS,CAACE,eAAjC;AACA,SAAKC,UAAL,GAAkBH,SAAS,CAACG,UAA5B;AACA,SAAKC,aAAL,GAAqBJ,SAAS,CAACI,aAA/B;AACD;;AAEOC,EAAAA,aAAa,CAACC,KAAD,EAAsBC,OAAtB,EAAqD;AACxE,SAAKC,YAAL,GAAoB,KAAKC,WAAzB;AACA,SAAKA,WAAL,GAAmBH,KAAK,CAACI,IAAzB;AAEA,UAAM,CAACC,cAAD,EAAiBC,eAAjB,IAAoC,KAAKC,WAA/C;AAEA,UAAMC,kBAAkB,GAAGP,OAAO,CAACQ,qBAAR,CAA8BJ,cAA9B,CAA3B;AACA,UAAMK,mBAAmB,GAAGT,OAAO,CAACQ,qBAAR,CAA8BH,eAA9B,CAA5B;AAEA,UAAMK,OAAe,GAAGD,mBAAmB,CAACE,CAApB,GAAwBJ,kBAAkB,CAACI,CAAnE;AACA,UAAMC,OAAe,GAAGH,mBAAmB,CAACI,CAApB,GAAwBN,kBAAkB,CAACM,CAAnE;AAEA,SAAKC,OAAL,GAAe,CAACP,kBAAkB,CAACI,CAAnB,GAAuBF,mBAAmB,CAACE,CAA5C,IAAiD,CAAhE;AACA,SAAKI,OAAL,GAAe,CAACR,kBAAkB,CAACM,CAAnB,GAAuBJ,mBAAmB,CAACI,CAA5C,IAAiD,CAAhE,CAbwE,CAexE;;AACA,UAAMG,KAAa,GAAG,CAACC,IAAI,CAACC,KAAL,CAAWN,OAAX,EAAoBF,OAApB,CAAvB;AAEA,SAAKS,QAAL,GAAgBC,MAAM,CAACC,KAAP,CAAa,KAAKC,aAAlB,IACZ,CADY,GAEZ,KAAKA,aAAL,GAAqBN,KAFzB;AAIA,SAAKM,aAAL,GAAqBN,KAArB;;AAEA,QAAI,KAAKG,QAAL,GAAgBF,IAAI,CAACM,EAAzB,EAA6B;AAC3B,WAAKJ,QAAL,IAAiBF,IAAI,CAACM,EAAtB;AACD,KAFD,MAEO,IAAI,KAAKJ,QAAL,GAAgB,CAACF,IAAI,CAACM,EAA1B,EAA8B;AACnC,WAAKJ,QAAL,IAAiBF,IAAI,CAACM,EAAtB;AACD;;AAED,QAAI,KAAKJ,QAAL,GAAgBF,IAAI,CAACM,EAAL,GAAU,CAA9B,EAAiC;AAC/B,WAAKJ,QAAL,IAAiBF,IAAI,CAACM,EAAtB;AACD,KAFD,MAEO,IAAI,KAAKJ,QAAL,GAAgB,CAACF,IAAI,CAACM,EAAN,GAAW,CAA/B,EAAkC;AACvC,WAAKJ,QAAL,IAAiBF,IAAI,CAACM,EAAtB;AACD;AACF;;AAEOC,EAAAA,MAAM,GAAS;AACrB,QAAI,CAAC,KAAKC,YAAV,EAAwB;AACtB;AACD;;AAED,SAAKA,YAAL,GAAoB,KAApB;AACA,SAAKnB,WAAL,GAAmB,CAACZ,GAAD,EAAMA,GAAN,CAAnB;AACA,SAAKG,aAAL,CAAmB,IAAnB;AACD;;AAEO6B,EAAAA,cAAc,CAAC1B,OAAD,EAAgC;AACpD,QAAI,KAAKM,WAAL,CAAiB,CAAjB,KAAuB,KAAKA,WAAL,CAAiB,CAAjB,CAA3B,EAAgD;AAC9C;AACD;;AAED,UAAMqB,UAAoC,GAAG3B,OAAO,CAAC4B,OAAR,GAAkBC,IAAlB,EAA7C;AAEA,SAAKvB,WAAL,CAAiB,CAAjB,IAAsBqB,UAAU,CAACG,IAAX,GAAkBC,KAAxC;AACA,SAAKzB,WAAL,CAAiB,CAAjB,IAAsBqB,UAAU,CAACG,IAAX,GAAkBC,KAAxC;AACD;;AAEMC,EAAAA,YAAY,CAACjC,KAAD,EAAsBC,OAAtB,EAAwD;AACzE,YAAQD,KAAK,CAACkC,SAAd;AACE,WAAKC,uBAAWC,IAAhB;AACE,aAAKV,YAAL,GAAoB,KAApB;AACA;;AAEF,WAAKS,uBAAWE,uBAAhB;AACE,YAAI,KAAKX,YAAT,EAAuB;AACrB;AACD;;AACD,aAAKA,YAAL,GAAoB,IAApB;AAEA,aAAKxB,YAAL,GAAoBF,KAAK,CAACI,IAA1B;AACA,aAAKmB,aAAL,GAAqB5B,GAArB;AAEA,aAAKgC,cAAL,CAAoB1B,OAApB;AAEA,aAAKF,aAAL,CAAmBC,KAAnB,EAA0BC,OAA1B;AACA,aAAKL,eAAL,CAAqB,IAArB;AACA;;AAEF,WAAKuC,uBAAWG,IAAhB;AACE,YAAI,CAAC,KAAKZ,YAAV,EAAwB;AACtB;AACD;;AAED,aAAK3B,aAAL,CAAmBC,KAAnB,EAA0BC,OAA1B;AACA,aAAKJ,UAAL,CAAgB,IAAhB;AAEA;;AAEF,WAAKsC,uBAAWI,qBAAhB;AACE,YAAI,CAAC,KAAKb,YAAV,EAAwB;AACtB;AACD;;AAED,YAAI,KAAKnB,WAAL,CAAiBiC,OAAjB,CAAyBxC,KAAK,CAACyC,SAA/B,KAA6C,CAAjD,EAAoD;AAClD,eAAKhB,MAAL;AACD;;AAED;;AAEF,WAAKU,uBAAWO,EAAhB;AACE,YAAI,KAAKhB,YAAT,EAAuB;AACrB,eAAKD,MAAL;AACD;;AACD;AA7CJ;;AAgDA,WAAO,IAAP;AACD;;AAEMkB,EAAAA,YAAY,GAAW;AAC5B,WAAO,KAAKxC,WAAL,GAAmB,KAAKD,YAA/B;AACD;;AAEM0C,EAAAA,UAAU,GAAW;AAC1B,WAAO,KAAK7B,OAAZ;AACD;;AAEM8B,EAAAA,UAAU,GAAW;AAC1B,WAAO,KAAK7B,OAAZ;AACD;;AAEM8B,EAAAA,WAAW,GAAW;AAC3B,WAAO,KAAK1B,QAAZ;AACD;;AAEM2B,EAAAA,KAAK,GAAS;AACnB,SAAKxC,WAAL,GAAmB,CAACZ,GAAD,EAAMA,GAAN,CAAnB;AACA,SAAK+B,YAAL,GAAoB,KAApB;AACD;;AAzJH", "sourcesContent": ["import { AdaptedEvent, EventTypes } from '../interfaces';\nimport PointerTracker from '../tools/PointerTracker';\n\nexport interface RotationGestureListener {\n  onRotationBegin: (detector: RotationGestureDetector) => boolean;\n  onRotation: (detector: RotationGestureDetector) => boolean;\n  onRotationEnd: (detector: RotationGestureDetector) => void;\n}\n\nexport default class RotationGestureDetector\n  implements RotationGestureListener\n{\n  onRotationBegin: (detector: RotationGestureDetector) => boolean;\n  onRotation: (detector: RotationGestureDetector) => boolean;\n  onRotationEnd: (detector: RotationGestureDetector) => void;\n\n  private currentTime = 0;\n  private previousTime = 0;\n\n  private previousAngle = 0;\n  private rotation = 0;\n\n  private anchorX = 0;\n  private anchorY = 0;\n\n  private isInProgress = false;\n\n  private keyPointers: number[] = [NaN, NaN];\n\n  constructor(callbacks: RotationGestureListener) {\n    this.onRotationBegin = callbacks.onRotationBegin;\n    this.onRotation = callbacks.onRotation;\n    this.onRotationEnd = callbacks.onRotationEnd;\n  }\n\n  private updateCurrent(event: AdaptedEvent, tracker: PointerTracker): void {\n    this.previousTime = this.currentTime;\n    this.currentTime = event.time;\n\n    const [firstPointerID, secondPointerID] = this.keyPointers;\n\n    const firstPointerCoords = tracker.getLastAbsoluteCoords(firstPointerID);\n    const secondPointerCoords = tracker.getLastAbsoluteCoords(secondPointerID);\n\n    const vectorX: number = secondPointerCoords.x - firstPointerCoords.x;\n    const vectorY: number = secondPointerCoords.y - firstPointerCoords.y;\n\n    this.anchorX = (firstPointerCoords.x + secondPointerCoords.x) / 2;\n    this.anchorY = (firstPointerCoords.y + secondPointerCoords.y) / 2;\n\n    // Angle diff should be positive when rotating in clockwise direction\n    const angle: number = -Math.atan2(vectorY, vectorX);\n\n    this.rotation = Number.isNaN(this.previousAngle)\n      ? 0\n      : this.previousAngle - angle;\n\n    this.previousAngle = angle;\n\n    if (this.rotation > Math.PI) {\n      this.rotation -= Math.PI;\n    } else if (this.rotation < -Math.PI) {\n      this.rotation += Math.PI;\n    }\n\n    if (this.rotation > Math.PI / 2) {\n      this.rotation -= Math.PI;\n    } else if (this.rotation < -Math.PI / 2) {\n      this.rotation += Math.PI;\n    }\n  }\n\n  private finish(): void {\n    if (!this.isInProgress) {\n      return;\n    }\n\n    this.isInProgress = false;\n    this.keyPointers = [NaN, NaN];\n    this.onRotationEnd(this);\n  }\n\n  private setKeyPointers(tracker: PointerTracker): void {\n    if (this.keyPointers[0] && this.keyPointers[1]) {\n      return;\n    }\n\n    const pointerIDs: IterableIterator<number> = tracker.getData().keys();\n\n    this.keyPointers[0] = pointerIDs.next().value as number;\n    this.keyPointers[1] = pointerIDs.next().value as number;\n  }\n\n  public onTouchEvent(event: AdaptedEvent, tracker: PointerTracker): boolean {\n    switch (event.eventType) {\n      case EventTypes.DOWN:\n        this.isInProgress = false;\n        break;\n\n      case EventTypes.ADDITIONAL_POINTER_DOWN:\n        if (this.isInProgress) {\n          break;\n        }\n        this.isInProgress = true;\n\n        this.previousTime = event.time;\n        this.previousAngle = NaN;\n\n        this.setKeyPointers(tracker);\n\n        this.updateCurrent(event, tracker);\n        this.onRotationBegin(this);\n        break;\n\n      case EventTypes.MOVE:\n        if (!this.isInProgress) {\n          break;\n        }\n\n        this.updateCurrent(event, tracker);\n        this.onRotation(this);\n\n        break;\n\n      case EventTypes.ADDITIONAL_POINTER_UP:\n        if (!this.isInProgress) {\n          break;\n        }\n\n        if (this.keyPointers.indexOf(event.pointerId) >= 0) {\n          this.finish();\n        }\n\n        break;\n\n      case EventTypes.UP:\n        if (this.isInProgress) {\n          this.finish();\n        }\n        break;\n    }\n\n    return true;\n  }\n\n  public getTimeDelta(): number {\n    return this.currentTime + this.previousTime;\n  }\n\n  public getAnchorX(): number {\n    return this.anchorX;\n  }\n\n  public getAnchorY(): number {\n    return this.anchorY;\n  }\n\n  public getRotation(): number {\n    return this.rotation;\n  }\n\n  public reset(): void {\n    this.keyPointers = [NaN, NaN];\n    this.isInProgress = false;\n  }\n}\n"]}