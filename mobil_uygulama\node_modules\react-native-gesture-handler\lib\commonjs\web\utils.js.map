{"version": 3, "sources": ["utils.ts"], "names": ["isPointerInBounds", "view", "x", "y", "rect", "getBoundingClientRect", "left", "right", "top", "bottom", "PointerTypeMapping", "Map", "PointerType", "MOUSE", "TOUCH", "STYLUS", "OTHER", "degToRad", "degrees", "Math", "PI", "coneToDeviation", "cos", "calculateViewScale", "styles", "getComputedStyle", "resultScales", "scaleX", "scaleY", "scale", "undefined", "scales", "split", "parseFloat", "matrixElements", "RegExp", "exec", "transform", "matrixElementsArray", "tryExtractStylusData", "event", "pointerType", "get", "eventAzimuthAngle", "azimuthAngle", "eventAltitudeAngle", "altitudeAngle", "tiltX", "tiltY", "pressure", "spherical2tilt", "tilt2spherical", "tiltXrad", "tiltYrad", "abs", "tanX", "tan", "tanY", "atan2", "atan", "sqrt", "pow", "radToDeg", "tanAlt", "sin", "round"], "mappings": ";;;;;;;;;;AAAA;;AAGO,SAASA,iBAAT,CAA2BC,IAA3B,EAA8C;AAAEC,EAAAA,CAAF;AAAKC,EAAAA;AAAL,CAA9C,EAAwE;AAC7E,QAAMC,IAAa,GAAGH,IAAI,CAACI,qBAAL,EAAtB;AAEA,SAAOH,CAAC,IAAIE,IAAI,CAACE,IAAV,IAAkBJ,CAAC,IAAIE,IAAI,CAACG,KAA5B,IAAqCJ,CAAC,IAAIC,IAAI,CAACI,GAA/C,IAAsDL,CAAC,IAAIC,IAAI,CAACK,MAAvE;AACD;;AAEM,MAAMC,kBAAkB,GAAG,IAAIC,GAAJ,CAA6B,CAC7D,CAAC,OAAD,EAAUC,yBAAYC,KAAtB,CAD6D,EAE7D,CAAC,OAAD,EAAUD,yBAAYE,KAAtB,CAF6D,EAG7D,CAAC,KAAD,EAAQF,yBAAYG,MAApB,CAH6D,EAI7D,CAAC,MAAD,EAASH,yBAAYI,KAArB,CAJ6D,CAA7B,CAA3B;;;AAOA,MAAMC,QAAQ,GAAIC,OAAD,IAAsBA,OAAO,GAAGC,IAAI,CAACC,EAAhB,GAAsB,GAA5D;;;;AAEA,MAAMC,eAAe,GAAIH,OAAD,IAC7BC,IAAI,CAACG,GAAL,CAASL,QAAQ,CAACC,OAAO,GAAG,CAAX,CAAjB,CADK;;;;AAGA,SAASK,kBAAT,CAA4BtB,IAA5B,EAA+C;AAAA;;AACpD,QAAMuB,MAAM,GAAGC,gBAAgB,CAACxB,IAAD,CAA/B;AAEA,QAAMyB,YAAY,GAAG;AACnBC,IAAAA,MAAM,EAAE,CADW;AAEnBC,IAAAA,MAAM,EAAE;AAFW,GAArB,CAHoD,CAQpD;;AACA,MAAIJ,MAAM,CAACK,KAAP,KAAiBC,SAAjB,IAA8BN,MAAM,CAACK,KAAP,KAAiB,MAAnD,EAA2D;AACzD,UAAME,MAAM,GAAGP,MAAM,CAACK,KAAP,CAAaG,KAAb,CAAmB,GAAnB,CAAf;;AAEA,QAAID,MAAM,CAAC,CAAD,CAAV,EAAe;AACbL,MAAAA,YAAY,CAACC,MAAb,GAAsBM,UAAU,CAACF,MAAM,CAAC,CAAD,CAAP,CAAhC;AACD;;AAEDL,IAAAA,YAAY,CAACE,MAAb,GAAsBG,MAAM,CAAC,CAAD,CAAN,GAClBE,UAAU,CAACF,MAAM,CAAC,CAAD,CAAP,CADQ,GAElBE,UAAU,CAACF,MAAM,CAAC,CAAD,CAAP,CAFd;AAGD,GAnBmD,CAqBpD;;;AACA,QAAMG,cAAc,mBAAG,IAAIC,MAAJ,CAAW,gBAAX,EAA6BC,IAA7B,CACrBZ,MAAM,CAACa,SADc,CAAH,iDAAG,aAEnB,CAFmB,CAAvB;;AAIA,MAAIH,cAAJ,EAAoB;AAClB,UAAMI,mBAAmB,GAAGJ,cAAc,CAACF,KAAf,CAAqB,IAArB,CAA5B;AAEAN,IAAAA,YAAY,CAACC,MAAb,IAAuBM,UAAU,CAACK,mBAAmB,CAAC,CAAD,CAApB,CAAjC;AACAZ,IAAAA,YAAY,CAACE,MAAb,IAAuBK,UAAU,CAACK,mBAAmB,CAAC,CAAD,CAApB,CAAjC;AACD;;AAED,SAAOZ,YAAP;AACD;;AAEM,SAASa,oBAAT,CACLC,KADK,EAEmB;AACxB,QAAMC,WAAW,GAAG/B,kBAAkB,CAACgC,GAAnB,CAAuBF,KAAK,CAACC,WAA7B,CAApB;;AAEA,MAAIA,WAAW,KAAK7B,yBAAYG,MAAhC,EAAwC;AACtC;AACD,GALuB,CAOxB;;;AACA,QAAM4B,iBAAqC,GAAGH,KAAK,CAACI,YAApD,CARwB,CASxB;;AACA,QAAMC,kBAAsC,GAAGL,KAAK,CAACM,aAArD;;AAEA,MAAIN,KAAK,CAACO,KAAN,KAAgB,CAAhB,IAAqBP,KAAK,CAACQ,KAAN,KAAgB,CAAzC,EAA4C;AAC1C;AACA;AAEA;AACA;AACA,QAAIL,iBAAiB,KAAKb,SAAtB,IAAmCe,kBAAkB,KAAKf,SAA9D,EAAyE;AACvE,aAAO;AACLiB,QAAAA,KAAK,EAAE,CADF;AAELC,QAAAA,KAAK,EAAE,CAFF;AAGLJ,QAAAA,YAAY,EAAEzB,IAAI,CAACC,EAAL,GAAU,CAHnB;AAIL0B,QAAAA,aAAa,EAAE3B,IAAI,CAACC,EAAL,GAAU,CAJpB;AAKL6B,QAAAA,QAAQ,EAAET,KAAK,CAACS;AALX,OAAP;AAOD;;AAED,UAAM;AAAEF,MAAAA,KAAF;AAASC,MAAAA;AAAT,QAAmBE,cAAc,CACrCL,kBADqC,EAErCF,iBAFqC,CAAvC;AAKA,WAAO;AACLI,MAAAA,KADK;AAELC,MAAAA,KAFK;AAGLJ,MAAAA,YAAY,EAAED,iBAHT;AAILG,MAAAA,aAAa,EAAED,kBAJV;AAKLI,MAAAA,QAAQ,EAAET,KAAK,CAACS;AALX,KAAP;AAOD;;AAED,QAAM;AAAEH,IAAAA,aAAF;AAAiBF,IAAAA;AAAjB,MAAkCO,cAAc,CACpDX,KAAK,CAACO,KAD8C,EAEpDP,KAAK,CAACQ,KAF8C,CAAtD;AAKA,SAAO;AACLD,IAAAA,KAAK,EAAEP,KAAK,CAACO,KADR;AAELC,IAAAA,KAAK,EAAER,KAAK,CAACQ,KAFR;AAGLJ,IAAAA,YAHK;AAILE,IAAAA,aAJK;AAKLG,IAAAA,QAAQ,EAAET,KAAK,CAACS;AALX,GAAP;AAOD,C,CAED;AACA;AACA;AACA;;;AACA,SAASE,cAAT,CAAwBJ,KAAxB,EAAuCC,KAAvC,EAAsD;AACpD,QAAMI,QAAQ,GAAIL,KAAK,GAAG5B,IAAI,CAACC,EAAd,GAAoB,GAArC;AACA,QAAMiC,QAAQ,GAAIL,KAAK,GAAG7B,IAAI,CAACC,EAAd,GAAoB,GAArC,CAFoD,CAIpD;;AACA,MAAIwB,YAAY,GAAG,CAAnB;;AAEA,MAAIG,KAAK,KAAK,CAAd,EAAiB;AACf,QAAIC,KAAK,GAAG,CAAZ,EAAe;AACbJ,MAAAA,YAAY,GAAGzB,IAAI,CAACC,EAAL,GAAU,CAAzB;AACD,KAFD,MAEO,IAAI4B,KAAK,GAAG,CAAZ,EAAe;AACpBJ,MAAAA,YAAY,GAAI,IAAIzB,IAAI,CAACC,EAAV,GAAgB,CAA/B;AACD;AACF,GAND,MAMO,IAAI4B,KAAK,KAAK,CAAd,EAAiB;AACtB,QAAID,KAAK,GAAG,CAAZ,EAAe;AACbH,MAAAA,YAAY,GAAGzB,IAAI,CAACC,EAApB;AACD;AACF,GAJM,MAIA,IAAID,IAAI,CAACmC,GAAL,CAASP,KAAT,MAAoB,EAApB,IAA0B5B,IAAI,CAACmC,GAAL,CAASN,KAAT,MAAoB,EAAlD,EAAsD;AAC3D;AACAJ,IAAAA,YAAY,GAAG,CAAf;AACD,GAHM,MAGA;AACL;AACA,UAAMW,IAAI,GAAGpC,IAAI,CAACqC,GAAL,CAASJ,QAAT,CAAb;AACA,UAAMK,IAAI,GAAGtC,IAAI,CAACqC,GAAL,CAASH,QAAT,CAAb;AAEAT,IAAAA,YAAY,GAAGzB,IAAI,CAACuC,KAAL,CAAWD,IAAX,EAAiBF,IAAjB,CAAf;;AACA,QAAIX,YAAY,GAAG,CAAnB,EAAsB;AACpBA,MAAAA,YAAY,IAAI,IAAIzB,IAAI,CAACC,EAAzB;AACD;AACF,GA7BmD,CA+BpD;;;AACA,MAAI0B,aAAa,GAAG,CAApB;;AAEA,MAAI3B,IAAI,CAACmC,GAAL,CAASP,KAAT,MAAoB,EAApB,IAA0B5B,IAAI,CAACmC,GAAL,CAASN,KAAT,MAAoB,EAAlD,EAAsD;AACpDF,IAAAA,aAAa,GAAG,CAAhB;AACD,GAFD,MAEO,IAAIC,KAAK,KAAK,CAAd,EAAiB;AACtBD,IAAAA,aAAa,GAAG3B,IAAI,CAACC,EAAL,GAAU,CAAV,GAAcD,IAAI,CAACmC,GAAL,CAASD,QAAT,CAA9B;AACD,GAFM,MAEA,IAAIL,KAAK,KAAK,CAAd,EAAiB;AACtBF,IAAAA,aAAa,GAAG3B,IAAI,CAACC,EAAL,GAAU,CAAV,GAAcD,IAAI,CAACmC,GAAL,CAASF,QAAT,CAA9B;AACD,GAFM,MAEA;AACL;AACAN,IAAAA,aAAa,GAAG3B,IAAI,CAACwC,IAAL,CACd,MACExC,IAAI,CAACyC,IAAL,CACEzC,IAAI,CAAC0C,GAAL,CAAS1C,IAAI,CAACqC,GAAL,CAASJ,QAAT,CAAT,EAA6B,CAA7B,IAAkCjC,IAAI,CAAC0C,GAAL,CAAS1C,IAAI,CAACqC,GAAL,CAASH,QAAT,CAAT,EAA6B,CAA7B,CADpC,CAFY,CAAhB;AAMD;;AAED,SAAO;AAAEP,IAAAA,aAAa,EAAEA,aAAjB;AAAgCF,IAAAA,YAAY,EAAEA;AAA9C,GAAP;AACD,C,CAED;AACA;AACA;;;AACA,SAASM,cAAT,CAAwBJ,aAAxB,EAA+CF,YAA/C,EAAqE;AACnE,QAAMkB,QAAQ,GAAG,MAAM3C,IAAI,CAACC,EAA5B;AAEA,MAAIgC,QAAQ,GAAG,CAAf;AACA,MAAIC,QAAQ,GAAG,CAAf;;AAEA,MAAIP,aAAa,KAAK,CAAtB,EAAyB;AACvB;AACA,QAAIF,YAAY,KAAK,CAAjB,IAAsBA,YAAY,KAAK,IAAIzB,IAAI,CAACC,EAApD,EAAwD;AACtD;AACAgC,MAAAA,QAAQ,GAAGjC,IAAI,CAACC,EAAL,GAAU,CAArB;AACD;;AACD,QAAIwB,YAAY,KAAKzB,IAAI,CAACC,EAAL,GAAU,CAA/B,EAAkC;AAChC;AACAiC,MAAAA,QAAQ,GAAGlC,IAAI,CAACC,EAAL,GAAU,CAArB;AACD;;AACD,QAAIwB,YAAY,KAAKzB,IAAI,CAACC,EAA1B,EAA8B;AAC5B;AACAgC,MAAAA,QAAQ,GAAG,CAACjC,IAAI,CAACC,EAAN,GAAW,CAAtB;AACD;;AACD,QAAIwB,YAAY,KAAM,IAAIzB,IAAI,CAACC,EAAV,GAAgB,CAArC,EAAwC;AACtC;AACAiC,MAAAA,QAAQ,GAAG,CAAClC,IAAI,CAACC,EAAN,GAAW,CAAtB;AACD;;AACD,QAAIwB,YAAY,GAAG,CAAf,IAAoBA,YAAY,GAAGzB,IAAI,CAACC,EAAL,GAAU,CAAjD,EAAoD;AAClDgC,MAAAA,QAAQ,GAAGjC,IAAI,CAACC,EAAL,GAAU,CAArB;AACAiC,MAAAA,QAAQ,GAAGlC,IAAI,CAACC,EAAL,GAAU,CAArB;AACD;;AACD,QAAIwB,YAAY,GAAGzB,IAAI,CAACC,EAAL,GAAU,CAAzB,IAA8BwB,YAAY,GAAGzB,IAAI,CAACC,EAAtD,EAA0D;AACxDgC,MAAAA,QAAQ,GAAG,CAACjC,IAAI,CAACC,EAAN,GAAW,CAAtB;AACAiC,MAAAA,QAAQ,GAAGlC,IAAI,CAACC,EAAL,GAAU,CAArB;AACD;;AACD,QAAIwB,YAAY,GAAGzB,IAAI,CAACC,EAApB,IAA0BwB,YAAY,GAAI,IAAIzB,IAAI,CAACC,EAAV,GAAgB,CAA7D,EAAgE;AAC9DgC,MAAAA,QAAQ,GAAG,CAACjC,IAAI,CAACC,EAAN,GAAW,CAAtB;AACAiC,MAAAA,QAAQ,GAAG,CAAClC,IAAI,CAACC,EAAN,GAAW,CAAtB;AACD;;AACD,QAAIwB,YAAY,GAAI,IAAIzB,IAAI,CAACC,EAAV,GAAgB,CAA/B,IAAoCwB,YAAY,GAAG,IAAIzB,IAAI,CAACC,EAAhE,EAAoE;AAClEgC,MAAAA,QAAQ,GAAGjC,IAAI,CAACC,EAAL,GAAU,CAArB;AACAiC,MAAAA,QAAQ,GAAG,CAAClC,IAAI,CAACC,EAAN,GAAW,CAAtB;AACD;AACF;;AAED,MAAI0B,aAAa,KAAK,CAAtB,EAAyB;AACvB,UAAMiB,MAAM,GAAG5C,IAAI,CAACqC,GAAL,CAASV,aAAT,CAAf;AAEAM,IAAAA,QAAQ,GAAGjC,IAAI,CAACwC,IAAL,CAAUxC,IAAI,CAACG,GAAL,CAASsB,YAAT,IAAyBmB,MAAnC,CAAX;AACAV,IAAAA,QAAQ,GAAGlC,IAAI,CAACwC,IAAL,CAAUxC,IAAI,CAAC6C,GAAL,CAASpB,YAAT,IAAyBmB,MAAnC,CAAX;AACD;;AAED,QAAMhB,KAAK,GAAG5B,IAAI,CAAC8C,KAAL,CAAWb,QAAQ,GAAGU,QAAtB,CAAd;AACA,QAAMd,KAAK,GAAG7B,IAAI,CAAC8C,KAAL,CAAWZ,QAAQ,GAAGS,QAAtB,CAAd;AAEA,SAAO;AAAEf,IAAAA,KAAF;AAASC,IAAAA;AAAT,GAAP;AACD", "sourcesContent": ["import { PointerType } from '../PointerType';\nimport type { Point, StylusData } from './interfaces';\n\nexport function isPointerInBounds(view: HTMLElement, { x, y }: Point): boolean {\n  const rect: DOMRect = view.getBoundingClientRect();\n\n  return x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;\n}\n\nexport const PointerTypeMapping = new Map<string, PointerType>([\n  ['mouse', PointerType.MOUSE],\n  ['touch', PointerType.TOUCH],\n  ['pen', PointerType.STYLUS],\n  ['none', PointerType.OTHER],\n]);\n\nexport const degToRad = (degrees: number) => (degrees * Math.PI) / 180;\n\nexport const coneToDeviation = (degrees: number) =>\n  Math.cos(degToRad(degrees / 2));\n\nexport function calculateViewScale(view: HTMLElement) {\n  const styles = getComputedStyle(view);\n\n  const resultScales = {\n    scaleX: 1,\n    scaleY: 1,\n  };\n\n  // Get scales from scale property\n  if (styles.scale !== undefined && styles.scale !== 'none') {\n    const scales = styles.scale.split(' ');\n\n    if (scales[0]) {\n      resultScales.scaleX = parseFloat(scales[0]);\n    }\n\n    resultScales.scaleY = scales[1]\n      ? parseFloat(scales[1])\n      : parseFloat(scales[0]);\n  }\n\n  // Get scales from transform property\n  const matrixElements = new RegExp(/matrix\\((.+)\\)/).exec(\n    styles.transform\n  )?.[1];\n\n  if (matrixElements) {\n    const matrixElementsArray = matrixElements.split(', ');\n\n    resultScales.scaleX *= parseFloat(matrixElementsArray[0]);\n    resultScales.scaleY *= parseFloat(matrixElementsArray[3]);\n  }\n\n  return resultScales;\n}\n\nexport function tryExtractStylusData(\n  event: PointerEvent\n): StylusData | undefined {\n  const pointerType = PointerTypeMapping.get(event.pointerType);\n\n  if (pointerType !== PointerType.STYLUS) {\n    return;\n  }\n\n  // @ts-ignore This property exists (https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent#instance_properties)\n  const eventAzimuthAngle: number | undefined = event.azimuthAngle;\n  // @ts-ignore This property exists (https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent#instance_properties)\n  const eventAltitudeAngle: number | undefined = event.altitudeAngle;\n\n  if (event.tiltX === 0 && event.tiltY === 0) {\n    // If we are in this branch, it means that either tilt properties are not supported and we have to calculate them from altitude and azimuth angles,\n    // or stylus is perpendicular to the screen and we can use altitude / azimuth instead of tilt\n\n    // If azimuth and altitude are undefined in this branch, it means that we are either perpendicular to the screen,\n    // or that none of the position sets is supported. In that case, we can treat stylus as perpendicular\n    if (eventAzimuthAngle === undefined || eventAltitudeAngle === undefined) {\n      return {\n        tiltX: 0,\n        tiltY: 0,\n        azimuthAngle: Math.PI / 2,\n        altitudeAngle: Math.PI / 2,\n        pressure: event.pressure,\n      };\n    }\n\n    const { tiltX, tiltY } = spherical2tilt(\n      eventAltitudeAngle,\n      eventAzimuthAngle\n    );\n\n    return {\n      tiltX,\n      tiltY,\n      azimuthAngle: eventAzimuthAngle,\n      altitudeAngle: eventAltitudeAngle,\n      pressure: event.pressure,\n    };\n  }\n\n  const { altitudeAngle, azimuthAngle } = tilt2spherical(\n    event.tiltX,\n    event.tiltY\n  );\n\n  return {\n    tiltX: event.tiltX,\n    tiltY: event.tiltY,\n    azimuthAngle,\n    altitudeAngle,\n    pressure: event.pressure,\n  };\n}\n\n// `altitudeAngle` and `azimuthAngle` are experimental properties, which are not supported on Firefox and Safari.\n// Given that, we use `tilt` properties and algorithm that converts one value to another.\n//\n// Source: https://w3c.github.io/pointerevents/#converting-between-tiltx-tilty-and-altitudeangle-azimuthangle\nfunction tilt2spherical(tiltX: number, tiltY: number) {\n  const tiltXrad = (tiltX * Math.PI) / 180;\n  const tiltYrad = (tiltY * Math.PI) / 180;\n\n  // calculate azimuth angle\n  let azimuthAngle = 0;\n\n  if (tiltX === 0) {\n    if (tiltY > 0) {\n      azimuthAngle = Math.PI / 2;\n    } else if (tiltY < 0) {\n      azimuthAngle = (3 * Math.PI) / 2;\n    }\n  } else if (tiltY === 0) {\n    if (tiltX < 0) {\n      azimuthAngle = Math.PI;\n    }\n  } else if (Math.abs(tiltX) === 90 || Math.abs(tiltY) === 90) {\n    // not enough information to calculate azimuth\n    azimuthAngle = 0;\n  } else {\n    // Non-boundary case: neither tiltX nor tiltY is equal to 0 or +-90\n    const tanX = Math.tan(tiltXrad);\n    const tanY = Math.tan(tiltYrad);\n\n    azimuthAngle = Math.atan2(tanY, tanX);\n    if (azimuthAngle < 0) {\n      azimuthAngle += 2 * Math.PI;\n    }\n  }\n\n  // calculate altitude angle\n  let altitudeAngle = 0;\n\n  if (Math.abs(tiltX) === 90 || Math.abs(tiltY) === 90) {\n    altitudeAngle = 0;\n  } else if (tiltX === 0) {\n    altitudeAngle = Math.PI / 2 - Math.abs(tiltYrad);\n  } else if (tiltY === 0) {\n    altitudeAngle = Math.PI / 2 - Math.abs(tiltXrad);\n  } else {\n    // Non-boundary case: neither tiltX nor tiltY is equal to 0 or +-90\n    altitudeAngle = Math.atan(\n      1.0 /\n        Math.sqrt(\n          Math.pow(Math.tan(tiltXrad), 2) + Math.pow(Math.tan(tiltYrad), 2)\n        )\n    );\n  }\n\n  return { altitudeAngle: altitudeAngle, azimuthAngle: azimuthAngle };\n}\n\n// If we are on a platform that doesn't support `tiltX` and `tiltY`, we have to calculate them from `altitude` and `azimuth` angles.\n//\n// Source: https://w3c.github.io/pointerevents/#converting-between-tiltx-tilty-and-altitudeangle-azimuthangle\nfunction spherical2tilt(altitudeAngle: number, azimuthAngle: number) {\n  const radToDeg = 180 / Math.PI;\n\n  let tiltXrad = 0;\n  let tiltYrad = 0;\n\n  if (altitudeAngle === 0) {\n    // the pen is in the X-Y plane\n    if (azimuthAngle === 0 || azimuthAngle === 2 * Math.PI) {\n      // pen is on positive X axis\n      tiltXrad = Math.PI / 2;\n    }\n    if (azimuthAngle === Math.PI / 2) {\n      // pen is on positive Y axis\n      tiltYrad = Math.PI / 2;\n    }\n    if (azimuthAngle === Math.PI) {\n      // pen is on negative X axis\n      tiltXrad = -Math.PI / 2;\n    }\n    if (azimuthAngle === (3 * Math.PI) / 2) {\n      // pen is on negative Y axis\n      tiltYrad = -Math.PI / 2;\n    }\n    if (azimuthAngle > 0 && azimuthAngle < Math.PI / 2) {\n      tiltXrad = Math.PI / 2;\n      tiltYrad = Math.PI / 2;\n    }\n    if (azimuthAngle > Math.PI / 2 && azimuthAngle < Math.PI) {\n      tiltXrad = -Math.PI / 2;\n      tiltYrad = Math.PI / 2;\n    }\n    if (azimuthAngle > Math.PI && azimuthAngle < (3 * Math.PI) / 2) {\n      tiltXrad = -Math.PI / 2;\n      tiltYrad = -Math.PI / 2;\n    }\n    if (azimuthAngle > (3 * Math.PI) / 2 && azimuthAngle < 2 * Math.PI) {\n      tiltXrad = Math.PI / 2;\n      tiltYrad = -Math.PI / 2;\n    }\n  }\n\n  if (altitudeAngle !== 0) {\n    const tanAlt = Math.tan(altitudeAngle);\n\n    tiltXrad = Math.atan(Math.cos(azimuthAngle) / tanAlt);\n    tiltYrad = Math.atan(Math.sin(azimuthAngle) / tanAlt);\n  }\n\n  const tiltX = Math.round(tiltXrad * radToDeg);\n  const tiltY = Math.round(tiltYrad * radToDeg);\n\n  return { tiltX, tiltY };\n}\n"]}