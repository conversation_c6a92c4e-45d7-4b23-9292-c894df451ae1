{"version": 3, "sources": ["pinchGesture.ts"], "names": ["ContinousBaseGesture", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "scaleChange", "scale", "PinchGesture", "constructor", "handler<PERSON>ame", "onChange", "callback", "handlers"], "mappings": "AAAA,SAASA,oBAAT,QAAqC,WAArC;;AAQA,SAASC,qBAAT,CACEC,OADF,EAEEC,QAFF,EAGE;AACA;;AACA,MAAIC,aAAJ;;AACA,MAAID,QAAQ,KAAKE,SAAjB,EAA4B;AAC1BD,IAAAA,aAAa,GAAG;AACdE,MAAAA,WAAW,EAAEJ,OAAO,CAACK;AADP,KAAhB;AAGD,GAJD,MAIO;AACLH,IAAAA,aAAa,GAAG;AACdE,MAAAA,WAAW,EAAEJ,OAAO,CAACK,KAAR,GAAgBJ,QAAQ,CAACI;AADxB,KAAhB;AAGD;;AAED,SAAO,EAAE,GAAGL,OAAL;AAAc,OAAGE;AAAjB,GAAP;AACD;;AAED,OAAO,MAAMI,YAAN,SAA2BR,oBAA3B,CAGL;AACAS,EAAAA,WAAW,GAAG;AACZ;AAEA,SAAKC,WAAL,GAAmB,qBAAnB;AACD;;AAEDC,EAAAA,QAAQ,CACNC,QADM,EAMN;AACA;AACA,SAAKC,QAAL,CAAcZ,qBAAd,GAAsCA,qBAAtC;AACA,WAAO,MAAMU,QAAN,CAAeC,QAAf,CAAP;AACD;;AAjBD", "sourcesContent": ["import { ContinousBaseGesture } from './gesture';\nimport type { PinchGestureHandlerEventPayload } from '../GestureHandlerEventPayload';\nimport { GestureUpdateEvent } from '../gestureHandlerCommon';\n\nexport type PinchGestureChangeEventPayload = {\n  scaleChange: number;\n};\n\nfunction changeEventCalculator(\n  current: GestureUpdateEvent<PinchGestureHandlerEventPayload>,\n  previous?: GestureUpdateEvent<PinchGestureHandlerEventPayload>\n) {\n  'worklet';\n  let changePayload: PinchGestureChangeEventPayload;\n  if (previous === undefined) {\n    changePayload = {\n      scaleChange: current.scale,\n    };\n  } else {\n    changePayload = {\n      scaleChange: current.scale / previous.scale,\n    };\n  }\n\n  return { ...current, ...changePayload };\n}\n\nexport class PinchGesture extends ContinousBaseGesture<\n  PinchGestureHandlerEventPayload,\n  PinchGestureChangeEventPayload\n> {\n  constructor() {\n    super();\n\n    this.handlerName = 'PinchGestureHandler';\n  }\n\n  onChange(\n    callback: (\n      event: GestureUpdateEvent<\n        PinchGestureHandlerEventPayload & PinchGestureChangeEventPayload\n      >\n    ) => void\n  ) {\n    // @ts-ignore TS being overprotective, PinchGestureHandlerEventPayload is Record\n    this.handlers.changeEventCalculator = changeEventCalculator;\n    return super.onChange(callback);\n  }\n}\n\nexport type PinchGestureType = InstanceType<typeof PinchGesture>;\n"]}