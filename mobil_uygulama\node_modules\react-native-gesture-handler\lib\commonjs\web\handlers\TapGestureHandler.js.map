{"version": 3, "sources": ["TapGestureHandler.ts"], "names": ["DEFAULT_MAX_DURATION_MS", "DEFAULT_MAX_DELAY_MS", "DEFAULT_NUMBER_OF_TAPS", "DEFAULT_MIN_NUMBER_OF_POINTERS", "TapGestureHandler", "Gesture<PERSON>andler", "Number", "MIN_SAFE_INTEGER", "init", "ref", "propsRef", "updateGestureConfig", "enabled", "props", "config", "numberOfTaps", "undefined", "maxDurationMs", "max<PERSON>elay<PERSON>", "maxDeltaX", "maxDeltaY", "maxDist", "maxDistSq", "minPointers", "minNumberOfPointers", "resetConfig", "clearTimeouts", "clearTimeout", "waitTimeout", "delayTimeout", "startTap", "setTimeout", "fail", "endTap", "tapsSoFar", "currentMaxNumberOfPointers", "activate", "onPointerDown", "event", "isButtonInConfig", "button", "tracker", "addToTracker", "trySettingPosition", "startX", "x", "startY", "y", "lastX", "lastY", "updateState", "tryToSendTouchEvent", "onPointerAdd", "offsetX", "offsetY", "lastCoords", "getAbsoluteCoordsAverage", "onPointerUp", "removeFromTracker", "pointerId", "onPointerRemove", "onPointerMove", "track", "onPointerOutOfBounds", "getTrackedPointersCount", "shouldFail", "currentState", "State", "UNDETERMINED", "eventType", "EventTypes", "DOWN", "begin", "BEGAN", "UP", "dx", "Math", "abs", "dy", "distSq", "end", "onCancel", "resetProgress"], "mappings": ";;;;;;;AAAA;;AACA;;AAEA;;;;;;AAEA,MAAMA,uBAAuB,GAAG,GAAhC;AACA,MAAMC,oBAAoB,GAAG,GAA7B;AACA,MAAMC,sBAAsB,GAAG,CAA/B;AACA,MAAMC,8BAA8B,GAAG,CAAvC;;AAEe,MAAMC,iBAAN,SAAgCC,uBAAhC,CAA+C;AAAA;AAAA;;AAAA,uCACxCC,MAAM,CAACC,gBADiC;;AAAA,uCAExCD,MAAM,CAACC,gBAFiC;;AAAA,uCAGxCD,MAAM,CAACC,gBAHiC;;AAAA,2CAIpCP,uBAJoC;;AAAA,wCAKvCC,oBALuC;;AAAA,0CAOrCC,sBAPqC;;AAAA,iDAQ9BC,8BAR8B;;AAAA,wDASvB,CATuB;;AAAA,oCAW3C,CAX2C;;AAAA,oCAY3C,CAZ2C;;AAAA,qCAa1C,CAb0C;;AAAA,qCAc1C,CAd0C;;AAAA,mCAe5C,CAf4C;;AAAA,mCAgB5C,CAhB4C;;AAAA;;AAAA;;AAAA,uCAqBxC,CArBwC;AAAA;;AAuBrDK,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAwD;AACjE,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AACD;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;;AAEA,QAAI,KAAKC,MAAL,CAAYC,YAAZ,KAA6BC,SAAjC,EAA4C;AAC1C,WAAKD,YAAL,GAAoB,KAAKD,MAAL,CAAYC,YAAhC;AACD;;AAED,QAAI,KAAKD,MAAL,CAAYG,aAAZ,KAA8BD,SAAlC,EAA6C;AAC3C,WAAKC,aAAL,GAAqB,KAAKH,MAAL,CAAYG,aAAjC;AACD;;AAED,QAAI,KAAKH,MAAL,CAAYI,UAAZ,KAA2BF,SAA/B,EAA0C;AACxC,WAAKE,UAAL,GAAkB,KAAKJ,MAAL,CAAYI,UAA9B;AACD;;AAED,QAAI,KAAKJ,MAAL,CAAYK,SAAZ,KAA0BH,SAA9B,EAAyC;AACvC,WAAKG,SAAL,GAAiB,KAAKL,MAAL,CAAYK,SAA7B;AACD;;AAED,QAAI,KAAKL,MAAL,CAAYM,SAAZ,KAA0BJ,SAA9B,EAAyC;AACvC,WAAKI,SAAL,GAAiB,KAAKN,MAAL,CAAYM,SAA7B;AACD;;AAED,QAAI,KAAKN,MAAL,CAAYO,OAAZ,KAAwBL,SAA5B,EAAuC;AACrC,WAAKM,SAAL,GAAiB,KAAKR,MAAL,CAAYO,OAAZ,GAAsB,KAAKP,MAAL,CAAYO,OAAnD;AACD;;AAED,QAAI,KAAKP,MAAL,CAAYS,WAAZ,KAA4BP,SAAhC,EAA2C;AACzC,WAAKQ,mBAAL,GAA2B,KAAKV,MAAL,CAAYS,WAAvC;AACD;AACF;;AAESE,EAAAA,WAAW,GAAS;AAC5B,UAAMA,WAAN;AAEA,SAAKN,SAAL,GAAiBb,MAAM,CAACC,gBAAxB;AACA,SAAKa,SAAL,GAAiBd,MAAM,CAACC,gBAAxB;AACA,SAAKe,SAAL,GAAiBhB,MAAM,CAACC,gBAAxB;AACA,SAAKU,aAAL,GAAqBjB,uBAArB;AACA,SAAKkB,UAAL,GAAkBjB,oBAAlB;AACA,SAAKc,YAAL,GAAoBb,sBAApB;AACA,SAAKsB,mBAAL,GAA2BrB,8BAA3B;AACD;;AAEOuB,EAAAA,aAAa,GAAS;AAC5BC,IAAAA,YAAY,CAAC,KAAKC,WAAN,CAAZ;AACAD,IAAAA,YAAY,CAAC,KAAKE,YAAN,CAAZ;AACD;;AAEOC,EAAAA,QAAQ,GAAS;AACvB,SAAKJ,aAAL;AAEA,SAAKE,WAAL,GAAmBG,UAAU,CAAC,MAAM,KAAKC,IAAL,EAAP,EAAoB,KAAKf,aAAzB,CAA7B;AACD;;AAEOgB,EAAAA,MAAM,GAAS;AACrB,SAAKP,aAAL;;AAEA,QACE,EAAE,KAAKQ,SAAP,KAAqB,KAAKnB,YAA1B,IACA,KAAKoB,0BAAL,IAAmC,KAAKX,mBAF1C,EAGE;AACA,WAAKY,QAAL;AACD,KALD,MAKO;AACL,WAAKP,YAAL,GAAoBE,UAAU,CAAC,MAAM,KAAKC,IAAL,EAAP,EAAoB,KAAKd,UAAzB,CAA9B;AACD;AACF,GA7F2D,CA+F5D;;;AACUmB,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,QAAI,CAAC,KAAKC,gBAAL,CAAsBD,KAAK,CAACE,MAA5B,CAAL,EAA0C;AACxC;AACD;;AAED,SAAKC,OAAL,CAAaC,YAAb,CAA0BJ,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AAEA,SAAKK,kBAAL,CAAwBL,KAAxB;AAEA,SAAKM,MAAL,GAAcN,KAAK,CAACO,CAApB;AACA,SAAKC,MAAL,GAAcR,KAAK,CAACS,CAApB;AAEA,SAAKC,KAAL,GAAaV,KAAK,CAACO,CAAnB;AACA,SAAKI,KAAL,GAAaX,KAAK,CAACS,CAAnB;AAEA,SAAKG,WAAL,CAAiBZ,KAAjB;AAEA,SAAKa,mBAAL,CAAyBb,KAAzB;AACD;;AAESc,EAAAA,YAAY,CAACd,KAAD,EAA4B;AAChD,UAAMc,YAAN,CAAmBd,KAAnB;AACA,SAAKG,OAAL,CAAaC,YAAb,CAA0BJ,KAA1B;AACA,SAAKK,kBAAL,CAAwBL,KAAxB;AAEA,SAAKe,OAAL,IAAgB,KAAKL,KAAL,GAAa,KAAKJ,MAAlC;AACA,SAAKU,OAAL,IAAgB,KAAKL,KAAL,GAAa,KAAKH,MAAlC;AAEA,UAAMS,UAAU,GAAG,KAAKd,OAAL,CAAae,wBAAb,EAAnB;AACA,SAAKR,KAAL,GAAaO,UAAU,CAACV,CAAxB;AACA,SAAKI,KAAL,GAAaM,UAAU,CAACR,CAAxB;AAEA,SAAKH,MAAL,GAAcW,UAAU,CAACV,CAAzB;AACA,SAAKC,MAAL,GAAcS,UAAU,CAACR,CAAzB;AAEA,SAAKG,WAAL,CAAiBZ,KAAjB;AACD;;AAESmB,EAAAA,WAAW,CAACnB,KAAD,EAA4B;AAC/C,UAAMmB,WAAN,CAAkBnB,KAAlB;AAEA,UAAMiB,UAAU,GAAG,KAAKd,OAAL,CAAae,wBAAb,EAAnB;AACA,SAAKR,KAAL,GAAaO,UAAU,CAACV,CAAxB;AACA,SAAKI,KAAL,GAAaM,UAAU,CAACR,CAAxB;AAEA,SAAKN,OAAL,CAAaiB,iBAAb,CAA+BpB,KAAK,CAACqB,SAArC;AAEA,SAAKT,WAAL,CAAiBZ,KAAjB;AACD;;AAESsB,EAAAA,eAAe,CAACtB,KAAD,EAA4B;AACnD,UAAMsB,eAAN,CAAsBtB,KAAtB;AACA,SAAKG,OAAL,CAAaiB,iBAAb,CAA+BpB,KAAK,CAACqB,SAArC;AAEA,SAAKN,OAAL,IAAgB,KAAKL,KAAL,GAAa,KAAKJ,MAAlC;AACA,SAAKU,OAAL,IAAgB,KAAKL,KAAL,GAAa,KAAKH,MAAlC;AAEA,UAAMS,UAAU,GAAG,KAAKd,OAAL,CAAae,wBAAb,EAAnB;AACA,SAAKR,KAAL,GAAaO,UAAU,CAACV,CAAxB;AACA,SAAKI,KAAL,GAAaM,UAAU,CAACR,CAAxB;AAEA,SAAKH,MAAL,GAAc,KAAKI,KAAnB;AACA,SAAKF,MAAL,GAAc,KAAKG,KAAnB;AAEA,SAAKC,WAAL,CAAiBZ,KAAjB;AACD;;AAESuB,EAAAA,aAAa,CAACvB,KAAD,EAA4B;AACjD,SAAKK,kBAAL,CAAwBL,KAAxB;AACA,SAAKG,OAAL,CAAaqB,KAAb,CAAmBxB,KAAnB;AAEA,UAAMiB,UAAU,GAAG,KAAKd,OAAL,CAAae,wBAAb,EAAnB;AACA,SAAKR,KAAL,GAAaO,UAAU,CAACV,CAAxB;AACA,SAAKI,KAAL,GAAaM,UAAU,CAACR,CAAxB;AAEA,SAAKG,WAAL,CAAiBZ,KAAjB;AAEA,UAAMuB,aAAN,CAAoBvB,KAApB;AACD;;AAESyB,EAAAA,oBAAoB,CAACzB,KAAD,EAA4B;AACxD,SAAKK,kBAAL,CAAwBL,KAAxB;AACA,SAAKG,OAAL,CAAaqB,KAAb,CAAmBxB,KAAnB;AAEA,UAAMiB,UAAU,GAAG,KAAKd,OAAL,CAAae,wBAAb,EAAnB;AACA,SAAKR,KAAL,GAAaO,UAAU,CAACV,CAAxB;AACA,SAAKI,KAAL,GAAaM,UAAU,CAACR,CAAxB;AAEA,SAAKG,WAAL,CAAiBZ,KAAjB;AAEA,UAAMyB,oBAAN,CAA2BzB,KAA3B;AACD;;AAEOY,EAAAA,WAAW,CAACZ,KAAD,EAA4B;AAC7C,QACE,KAAKH,0BAAL,GAAkC,KAAKM,OAAL,CAAauB,uBAAb,EADpC,EAEE;AACA,WAAK7B,0BAAL,GAAkC,KAAKM,OAAL,CAAauB,uBAAb,EAAlC;AACD;;AAED,QAAI,KAAKC,UAAL,EAAJ,EAAuB;AACrB,WAAKjC,IAAL;AACA;AACD;;AAED,YAAQ,KAAKkC,YAAb;AACE,WAAKC,aAAMC,YAAX;AACE,YAAI9B,KAAK,CAAC+B,SAAN,KAAoBC,uBAAWC,IAAnC,EAAyC;AACvC,eAAKC,KAAL;AACD;;AACD,aAAK1C,QAAL;AACA;;AACF,WAAKqC,aAAMM,KAAX;AACE,YAAInC,KAAK,CAAC+B,SAAN,KAAoBC,uBAAWI,EAAnC,EAAuC;AACrC,eAAKzC,MAAL;AACD;;AACD,YAAIK,KAAK,CAAC+B,SAAN,KAAoBC,uBAAWC,IAAnC,EAAyC;AACvC,eAAKzC,QAAL;AACD;;AACD;;AACF;AACE;AAhBJ;AAkBD;;AAEOa,EAAAA,kBAAkB,CAACL,KAAD,EAA4B;AACpD,QAAI,KAAK4B,YAAL,KAAsBC,aAAMC,YAAhC,EAA8C;AAC5C;AACD;;AAED,SAAKf,OAAL,GAAe,CAAf;AACA,SAAKC,OAAL,GAAe,CAAf;AACA,SAAKV,MAAL,GAAcN,KAAK,CAACO,CAApB;AACA,SAAKC,MAAL,GAAcR,KAAK,CAACS,CAApB;AACD;;AAEOkB,EAAAA,UAAU,GAAY;AAC5B,UAAMU,EAAE,GAAG,KAAK3B,KAAL,GAAa,KAAKJ,MAAlB,GAA2B,KAAKS,OAA3C;;AAEA,QACE,KAAKlC,SAAL,KAAmBb,MAAM,CAACC,gBAA1B,IACAqE,IAAI,CAACC,GAAL,CAASF,EAAT,IAAe,KAAKxD,SAFtB,EAGE;AACA,aAAO,IAAP;AACD;;AAED,UAAM2D,EAAE,GAAG,KAAK7B,KAAL,GAAa,KAAKH,MAAlB,GAA2B,KAAKQ,OAA3C;;AACA,QACE,KAAKlC,SAAL,KAAmBd,MAAM,CAACC,gBAA1B,IACAqE,IAAI,CAACC,GAAL,CAASC,EAAT,IAAe,KAAK1D,SAFtB,EAGE;AACA,aAAO,IAAP;AACD;;AAED,UAAM2D,MAAM,GAAGD,EAAE,GAAGA,EAAL,GAAUH,EAAE,GAAGA,EAA9B;AAEA,WACE,KAAKrD,SAAL,KAAmBhB,MAAM,CAACC,gBAA1B,IAA8CwE,MAAM,GAAG,KAAKzD,SAD9D;AAGD;;AAEMc,EAAAA,QAAQ,GAAS;AACtB,UAAMA,QAAN;AAEA,SAAK4C,GAAL;AACD;;AAESC,EAAAA,QAAQ,GAAS;AACzB,SAAKC,aAAL;AACA,SAAKxD,aAAL;AACD;;AAESwD,EAAAA,aAAa,GAAS;AAC9B,SAAKxD,aAAL;AACA,SAAKQ,SAAL,GAAiB,CAAjB;AACA,SAAKC,0BAAL,GAAkC,CAAlC;AACD;;AAjR2D", "sourcesContent": ["import { State } from '../../State';\nimport { AdaptedEvent, Config, EventTypes } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\n\nconst DEFAULT_MAX_DURATION_MS = 500;\nconst DEFAULT_MAX_DELAY_MS = 500;\nconst DEFAULT_NUMBER_OF_TAPS = 1;\nconst DEFAULT_MIN_NUMBER_OF_POINTERS = 1;\n\nexport default class TapGestureHandler extends GestureHandler {\n  private maxDeltaX = Number.MIN_SAFE_INTEGER;\n  private maxDeltaY = Number.MIN_SAFE_INTEGER;\n  private maxDistSq = Number.MIN_SAFE_INTEGER;\n  private maxDurationMs = DEFAULT_MAX_DURATION_MS;\n  private maxDelayMs = DEFAULT_MAX_DELAY_MS;\n\n  private numberOfTaps = DEFAULT_NUMBER_OF_TAPS;\n  private minNumberOfPointers = DEFAULT_MIN_NUMBER_OF_POINTERS;\n  private currentMaxNumberOfPointers = 1;\n\n  private startX = 0;\n  private startY = 0;\n  private offsetX = 0;\n  private offsetY = 0;\n  private lastX = 0;\n  private lastY = 0;\n\n  private waitTimeout: number | undefined;\n  private delayTimeout: number | undefined;\n\n  private tapsSoFar = 0;\n\n  public init(ref: number, propsRef: React.RefObject<unknown>): void {\n    super.init(ref, propsRef);\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n\n    if (this.config.numberOfTaps !== undefined) {\n      this.numberOfTaps = this.config.numberOfTaps;\n    }\n\n    if (this.config.maxDurationMs !== undefined) {\n      this.maxDurationMs = this.config.maxDurationMs;\n    }\n\n    if (this.config.maxDelayMs !== undefined) {\n      this.maxDelayMs = this.config.maxDelayMs;\n    }\n\n    if (this.config.maxDeltaX !== undefined) {\n      this.maxDeltaX = this.config.maxDeltaX;\n    }\n\n    if (this.config.maxDeltaY !== undefined) {\n      this.maxDeltaY = this.config.maxDeltaY;\n    }\n\n    if (this.config.maxDist !== undefined) {\n      this.maxDistSq = this.config.maxDist * this.config.maxDist;\n    }\n\n    if (this.config.minPointers !== undefined) {\n      this.minNumberOfPointers = this.config.minPointers;\n    }\n  }\n\n  protected resetConfig(): void {\n    super.resetConfig();\n\n    this.maxDeltaX = Number.MIN_SAFE_INTEGER;\n    this.maxDeltaY = Number.MIN_SAFE_INTEGER;\n    this.maxDistSq = Number.MIN_SAFE_INTEGER;\n    this.maxDurationMs = DEFAULT_MAX_DURATION_MS;\n    this.maxDelayMs = DEFAULT_MAX_DELAY_MS;\n    this.numberOfTaps = DEFAULT_NUMBER_OF_TAPS;\n    this.minNumberOfPointers = DEFAULT_MIN_NUMBER_OF_POINTERS;\n  }\n\n  private clearTimeouts(): void {\n    clearTimeout(this.waitTimeout);\n    clearTimeout(this.delayTimeout);\n  }\n\n  private startTap(): void {\n    this.clearTimeouts();\n\n    this.waitTimeout = setTimeout(() => this.fail(), this.maxDurationMs);\n  }\n\n  private endTap(): void {\n    this.clearTimeouts();\n\n    if (\n      ++this.tapsSoFar === this.numberOfTaps &&\n      this.currentMaxNumberOfPointers >= this.minNumberOfPointers\n    ) {\n      this.activate();\n    } else {\n      this.delayTimeout = setTimeout(() => this.fail(), this.maxDelayMs);\n    }\n  }\n\n  // Handling Events\n  protected onPointerDown(event: AdaptedEvent): void {\n    if (!this.isButtonInConfig(event.button)) {\n      return;\n    }\n\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n\n    this.trySettingPosition(event);\n\n    this.startX = event.x;\n    this.startY = event.y;\n\n    this.lastX = event.x;\n    this.lastY = event.y;\n\n    this.updateState(event);\n\n    this.tryToSendTouchEvent(event);\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    super.onPointerAdd(event);\n    this.tracker.addToTracker(event);\n    this.trySettingPosition(event);\n\n    this.offsetX += this.lastX - this.startX;\n    this.offsetY += this.lastY - this.startY;\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.lastX = lastCoords.x;\n    this.lastY = lastCoords.y;\n\n    this.startX = lastCoords.x;\n    this.startY = lastCoords.y;\n\n    this.updateState(event);\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.lastX = lastCoords.x;\n    this.lastY = lastCoords.y;\n\n    this.tracker.removeFromTracker(event.pointerId);\n\n    this.updateState(event);\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.tracker.removeFromTracker(event.pointerId);\n\n    this.offsetX += this.lastX - this.startX;\n    this.offsetY += this.lastY = this.startY;\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.lastX = lastCoords.x;\n    this.lastY = lastCoords.y;\n\n    this.startX = this.lastX;\n    this.startY = this.lastY;\n\n    this.updateState(event);\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.trySettingPosition(event);\n    this.tracker.track(event);\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.lastX = lastCoords.x;\n    this.lastY = lastCoords.y;\n\n    this.updateState(event);\n\n    super.onPointerMove(event);\n  }\n\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    this.trySettingPosition(event);\n    this.tracker.track(event);\n\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    this.lastX = lastCoords.x;\n    this.lastY = lastCoords.y;\n\n    this.updateState(event);\n\n    super.onPointerOutOfBounds(event);\n  }\n\n  private updateState(event: AdaptedEvent): void {\n    if (\n      this.currentMaxNumberOfPointers < this.tracker.getTrackedPointersCount()\n    ) {\n      this.currentMaxNumberOfPointers = this.tracker.getTrackedPointersCount();\n    }\n\n    if (this.shouldFail()) {\n      this.fail();\n      return;\n    }\n\n    switch (this.currentState) {\n      case State.UNDETERMINED:\n        if (event.eventType === EventTypes.DOWN) {\n          this.begin();\n        }\n        this.startTap();\n        break;\n      case State.BEGAN:\n        if (event.eventType === EventTypes.UP) {\n          this.endTap();\n        }\n        if (event.eventType === EventTypes.DOWN) {\n          this.startTap();\n        }\n        break;\n      default:\n        break;\n    }\n  }\n\n  private trySettingPosition(event: AdaptedEvent): void {\n    if (this.currentState !== State.UNDETERMINED) {\n      return;\n    }\n\n    this.offsetX = 0;\n    this.offsetY = 0;\n    this.startX = event.x;\n    this.startY = event.y;\n  }\n\n  private shouldFail(): boolean {\n    const dx = this.lastX - this.startX + this.offsetX;\n\n    if (\n      this.maxDeltaX !== Number.MIN_SAFE_INTEGER &&\n      Math.abs(dx) > this.maxDeltaX\n    ) {\n      return true;\n    }\n\n    const dy = this.lastY - this.startY + this.offsetY;\n    if (\n      this.maxDeltaY !== Number.MIN_SAFE_INTEGER &&\n      Math.abs(dy) > this.maxDeltaY\n    ) {\n      return true;\n    }\n\n    const distSq = dy * dy + dx * dx;\n\n    return (\n      this.maxDistSq !== Number.MIN_SAFE_INTEGER && distSq > this.maxDistSq\n    );\n  }\n\n  public activate(): void {\n    super.activate();\n\n    this.end();\n  }\n\n  protected onCancel(): void {\n    this.resetProgress();\n    this.clearTimeouts();\n  }\n\n  protected resetProgress(): void {\n    this.clearTimeouts();\n    this.tapsSoFar = 0;\n    this.currentMaxNumberOfPointers = 0;\n  }\n}\n"]}