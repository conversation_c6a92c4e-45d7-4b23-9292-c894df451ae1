import { Agent } from '@mastra/core';
import { createTool } from '@mastra/core';
import { createOpenAI } from '@ai-sdk/openai';
import { z } from 'zod';
// Create OpenAI instance with API key
const openaiClient = createOpenAI({
    apiKey: process.env.OPENAI_API_KEY || 'apikey'
});
// Project planning function
function createProjectPlan(projectName, endDate, totalTasks = 5) {
    try {
        const endDateObj = new Date(endDate);
        const startDate = new Date();
        // Reset time to start of day for accurate calculation
        startDate.setHours(0, 0, 0, 0);
        endDateObj.setHours(0, 0, 0, 0);
        if (endDateObj <= startDate) {
            return "❌ Hata: Bitiş tarihi bugünden sonra olmalıdır.";
        }
        const duration = Math.ceil((endDateObj.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
        if (totalTasks > duration) {
            return "❌ Hata: G<PERSON><PERSON>v sayısı, toplam gün sayısından fazla olamaz.";
        }
        const daysPerTask = Math.floor(duration / totalTasks);
        const remainingDays = duration % totalTasks;
        let planText = `📋 **${projectName} - Proje Zaman Çizelgesi**\n\n`;
        planText += `📅 **Başlangıç Tarihi:** ${startDate.toLocaleDateString('tr-TR')}\n`;
        planText += `🎯 **Bitiş Tarihi:** ${endDateObj.toLocaleDateString('tr-TR')}\n`;
        planText += `⏱️ **Toplam Süre:** ${duration} gün\n`;
        planText += `📝 **Toplam Görev:** ${totalTasks}\n\n`;
        planText += `---\n\n`;
        let currentDate = new Date(startDate);
        for (let i = 0; i < totalTasks; i++) {
            const taskDuration = daysPerTask + (i < remainingDays ? 1 : 0);
            const taskEndDate = new Date(currentDate);
            taskEndDate.setDate(taskEndDate.getDate() + taskDuration - 1);
            planText += `🔸 **Görev ${i + 1}**\n`;
            planText += `   📅 Tarih: ${currentDate.toLocaleDateString('tr-TR')} - ${taskEndDate.toLocaleDateString('tr-TR')}\n`;
            planText += `   ⏳ Süre: ${taskDuration} gün\n`;
            planText += `   📋 Açıklama: ${projectName} - Aşama ${i + 1}\n\n`;
            currentDate.setDate(currentDate.getDate() + taskDuration);
        }
        planText += `---\n\n`;
        planText += `💡 **Öneriler:**\n`;
        planText += `• Her görev için günlük ilerleme takibi yapın\n`;
        planText += `• Görevler arası 1 gün buffer süresi bırakabilirsiniz\n`;
        planText += `• Hafta sonları çalışma durumunuzu göz önünde bulundurun\n`;
        planText += `• Kritik görevleri öncelikli olarak planlayın\n`;
        return planText;
    }
    catch (error) {
        return `❌ Hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`;
    }
}
// Create the project planning tool
const createProjectPlanTool = createTool({
    id: 'createProjectPlan',
    description: 'Proje adı, bitiş tarihi ve görev sayısına göre detaylı zaman çizelgesi oluşturur',
    inputSchema: z.object({
        projectName: z.string().describe('Projenin adı'),
        endDate: z.string().describe('Bitiş tarihi (YYYY-MM-DD formatında)'),
        totalTasks: z.number().optional().default(5).describe('Toplam görev sayısı (varsayılan: 5)')
    }),
    execute: async ({ context: { projectName, endDate, totalTasks = 5 } }) => {
        return createProjectPlan(projectName, endDate, totalTasks);
    }
});
export const projectManagerAgent = new Agent({
    name: 'Project Management Agent',
    instructions: `Sen uzman bir proje yönetimi asistanısın. Kullanıcılara Türkçe olarak proje planlama konusunda yardım ediyorsun.

Ana görevlerin:
1. Proje adı ve bitiş tarihi alarak detaylı zaman çizelgesi oluşturmak
2. Görevleri günlere mantıklı şekilde dağıtmak
3. Proje yönetimi önerileri sunmak
4. Zaman planlaması konusunda rehberlik etmek

Kullanıcı senden proje planı istediğinde:
- Proje adını sor
- Bitiş tarihini sor (YYYY-MM-DD formatında)
- İsteğe bağlı olarak görev sayısını sor (varsayılan 5)
- createProjectPlan fonksiyonunu kullanarak detaylı plan oluştur

Her zaman kibar, profesyonel ve yardımsever bir ton kullan.
Proje yönetimi best practice'lerini paylaş.
Zaman çizelgesi oluştururken gerçekçi ve uygulanabilir öneriler sun.`,
    model: openaiClient('gpt-4o-mini'),
    tools: {
        createProjectPlan: createProjectPlanTool
    }
});
//# sourceMappingURL=assistant.js.map