{"version": 3, "sources": ["Swipeable.tsx"], "names": ["React", "Component", "Animated", "StyleSheet", "View", "I18nManager", "PanGestureHandler", "TapGestureHandler", "State", "DRAG_TOSS", "Swipeable", "constructor", "props", "state", "friction", "overshootFriction", "dragX", "rowTranslation", "leftWidth", "row<PERSON>id<PERSON>", "rightOffset", "rightWidth", "Math", "max", "overshootLeft", "overshootRight", "transX", "add", "interpolate", "inputRange", "outputRange", "showLeftAction", "Value", "leftActionTranslate", "Number", "MIN_VALUE", "extrapolate", "showRightAction", "rightActionTranslate", "nativeEvent", "oldState", "ACTIVE", "close", "ev", "handleRelease", "velocityX", "translationX", "rowState", "direction", "onSwipeableOpenStartDrag", "onSwipeableCloseStartDrag", "leftThreshold", "rightT<PERSON><PERSON><PERSON>", "startOffsetX", "currentOffset", "toValue", "animateRow", "fromValue", "setValue", "setState", "sign", "spring", "restSpeedThreshold", "restDisplacementThreshold", "velocity", "bounciness", "useNativeDriver", "useNativeAnimations", "animationOptions", "start", "finished", "onSwipeableLeftOpen", "onSwipeableOpen", "onSwipeableRightOpen", "closingDirection", "onSwipeableClose", "onSwipeableLeftWillOpen", "onSwipeableWillOpen", "onSwipeableRightWillOpen", "onSwipeableWillClose", "layout", "width", "undefined", "updateAnimatedEvent", "onGestureEvent", "event", "shouldComponentUpdate", "render", "children", "renderLeftActions", "renderRightActions", "dragOffsetFromLeftEdge", "dragOffsetFromRightEdge", "left", "styles", "leftActions", "transform", "translateX", "x", "right", "rightActions", "onHandlerStateChange", "onRowLayout", "container", "containerStyle", "onTapHandlerStateChange", "childrenContainerStyle", "create", "overflow", "absoluteFillObject", "flexDirection", "isRTL"], "mappings": ";;;;AAAA;AACA;AACA;AAEA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AACA,SAASC,SAAT,QAA0B,OAA1B;AACA,SACEC,QADF,EAEEC,UAFF,EAGEC,IAHF,EAIEC,WAJF,QAQO,cARP;AAcA,SACEC,iBADF,QAGO,+BAHP;AAQA,SAASC,iBAAT,QAAkC,+BAAlC;AACA,SAASC,KAAT,QAAsB,UAAtB;AAEA,MAAMC,SAAS,GAAG,IAAlB;;AAiMA;AACA;AACA;AACA;AACA;AAEA,eAAe,MAAMC,SAAN,SAAwBT,SAAxB,CAGb;AAOAU,EAAAA,WAAW,CAACC,MAAD,EAAwB;AACjC,UAAMA,MAAN;;AADiC;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,iDA4CL,CAC5BA,KAD4B,EAE5BC,KAF4B,KAGzB;AACH,YAAM;AAAEC,QAAAA,QAAF;AAAYC,QAAAA;AAAZ,UAAkCH,KAAxC;AACA,YAAM;AAAEI,QAAAA,KAAF;AAASC,QAAAA,cAAT;AAAyBC,QAAAA,SAAS,GAAG,CAArC;AAAwCC,QAAAA,QAAQ,GAAG;AAAnD,UAAyDN,KAA/D;AACA,YAAM;AAAEO,QAAAA,WAAW,GAAGD;AAAhB,UAA6BN,KAAnC;AACA,YAAMQ,UAAU,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYJ,QAAQ,GAAGC,WAAvB,CAAnB;AAEA,YAAM;AAAEI,QAAAA,aAAa,GAAGN,SAAS,GAAG,CAA9B;AAAiCO,QAAAA,cAAc,GAAGJ,UAAU,GAAG;AAA/D,UACJT,KADF;AAGA,YAAMc,MAAM,GAAGxB,QAAQ,CAACyB,GAAT,CACbV,cADa,EAEbD,KAAK,CAACY,WAAN,CAAkB;AAChBC,QAAAA,UAAU,EAAE,CAAC,CAAD,EAAIf,QAAJ,CADI;AAEhBgB,QAAAA,WAAW,EAAE,CAAC,CAAD,EAAI,CAAJ;AAFG,OAAlB,CAFa,EAMbF,WANa,CAMD;AACZC,QAAAA,UAAU,EAAE,CAAC,CAACR,UAAD,GAAc,CAAf,EAAkB,CAACA,UAAnB,EAA+BH,SAA/B,EAA0CA,SAAS,GAAG,CAAtD,CADA;AAEZY,QAAAA,WAAW,EAAE,CACX,CAACT,UAAD,IAAeI,cAAc,GAAG,IAAIV,iBAAP,GAA4B,CAAzD,CADW,EAEX,CAACM,UAFU,EAGXH,SAHW,EAIXA,SAAS,IAAIM,aAAa,GAAG,IAAIT,iBAAP,GAA4B,CAA7C,CAJE;AAFD,OANC,CAAf;AAeA,WAAKW,MAAL,GAAcA,MAAd;AACA,WAAKK,cAAL,GACEb,SAAS,GAAG,CAAZ,GACIQ,MAAM,CAACE,WAAP,CAAmB;AACjBC,QAAAA,UAAU,EAAE,CAAC,CAAC,CAAF,EAAK,CAAL,EAAQX,SAAR,CADK;AAEjBY,QAAAA,WAAW,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP;AAFI,OAAnB,CADJ,GAKI,IAAI5B,QAAQ,CAAC8B,KAAb,CAAmB,CAAnB,CANN;AAOA,WAAKC,mBAAL,GAA2B,KAAKF,cAAL,CAAoBH,WAApB,CAAgC;AACzDC,QAAAA,UAAU,EAAE,CAAC,CAAD,EAAIK,MAAM,CAACC,SAAX,CAD6C;AAEzDL,QAAAA,WAAW,EAAE,CAAC,CAAC,KAAF,EAAS,CAAT,CAF4C;AAGzDM,QAAAA,WAAW,EAAE;AAH4C,OAAhC,CAA3B;AAKA,WAAKC,eAAL,GACEhB,UAAU,GAAG,CAAb,GACIK,MAAM,CAACE,WAAP,CAAmB;AACjBC,QAAAA,UAAU,EAAE,CAAC,CAACR,UAAF,EAAc,CAAd,EAAiB,CAAjB,CADK;AAEjBS,QAAAA,WAAW,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP;AAFI,OAAnB,CADJ,GAKI,IAAI5B,QAAQ,CAAC8B,KAAb,CAAmB,CAAnB,CANN;AAOA,WAAKM,oBAAL,GAA4B,KAAKD,eAAL,CAAqBT,WAArB,CAAiC;AAC3DC,QAAAA,UAAU,EAAE,CAAC,CAAD,EAAIK,MAAM,CAACC,SAAX,CAD+C;AAE3DL,QAAAA,WAAW,EAAE,CAAC,CAAC,KAAF,EAAS,CAAT,CAF8C;AAG3DM,QAAAA,WAAW,EAAE;AAH8C,OAAjC,CAA5B;AAKD,KAhGkC;;AAAA,qDAkGD,CAAC;AACjCG,MAAAA;AADiC,KAAD,KAE4B;AAC5D,UAAIA,WAAW,CAACC,QAAZ,KAAyBhC,KAAK,CAACiC,MAAnC,EAA2C;AACzC,aAAKC,KAAL;AACD;AACF,KAxGkC;;AAAA,kDA2GjCC,EAD6B,IAE1B;AACH,UAAIA,EAAE,CAACJ,WAAH,CAAeC,QAAf,KAA4BhC,KAAK,CAACiC,MAAtC,EAA8C;AAC5C,aAAKG,aAAL,CAAmBD,EAAnB;AACD;;AAED,UAAIA,EAAE,CAACJ,WAAH,CAAe1B,KAAf,KAAyBL,KAAK,CAACiC,MAAnC,EAA2C;AACzC,cAAM;AAAEI,UAAAA,SAAF;AAAaC,UAAAA,YAAY,EAAE9B;AAA3B,YAAqC2B,EAAE,CAACJ,WAA9C;AACA,cAAM;AAAEQ,UAAAA;AAAF,YAAe,KAAKlC,KAA1B;AACA,cAAM;AAAEC,UAAAA;AAAF,YAAe,KAAKF,KAA1B;AAEA,cAAMkC,YAAY,GAAG,CAAC9B,KAAK,GAAGP,SAAS,GAAGoC,SAArB,IAAkC/B,QAAvD;AAEA,cAAMkC,SAAS,GACbD,QAAQ,KAAK,CAAC,CAAd,GACI,OADJ,GAEIA,QAAQ,KAAK,CAAb,GACA,MADA,GAEAD,YAAY,GAAG,CAAf,GACA,MADA,GAEA,OAPN;;AASA,YAAIC,QAAQ,KAAK,CAAjB,EAAoB;AAAA;;AAClB,uDAAKnC,KAAL,EAAWqC,wBAAX,kGAAsCD,SAAtC;AACD,SAFD,MAEO;AAAA;;AACL,yDAAKpC,KAAL,EAAWsC,yBAAX,qGAAuCF,SAAvC;AACD;AACF;AACF,KAvIkC;;AAAA,2CA0IjCL,EADsB,IAEnB;AACH,YAAM;AAAEE,QAAAA,SAAF;AAAaC,QAAAA,YAAY,EAAE9B;AAA3B,UAAqC2B,EAAE,CAACJ,WAA9C;AACA,YAAM;AAAErB,QAAAA,SAAS,GAAG,CAAd;AAAiBC,QAAAA,QAAQ,GAAG,CAA5B;AAA+B4B,QAAAA;AAA/B,UAA4C,KAAKlC,KAAvD;AACA,YAAM;AAAEO,QAAAA,WAAW,GAAGD;AAAhB,UAA6B,KAAKN,KAAxC;AACA,YAAMQ,UAAU,GAAGF,QAAQ,GAAGC,WAA9B;AACA,YAAM;AACJN,QAAAA,QADI;AAEJqC,QAAAA,aAAa,GAAGjC,SAAS,GAAG,CAFxB;AAGJkC,QAAAA,cAAc,GAAG/B,UAAU,GAAG;AAH1B,UAIF,KAAKT,KAJT;AAMA,YAAMyC,YAAY,GAAG,KAAKC,aAAL,KAAuBtC,KAAK,GAAGF,QAApD;AACA,YAAMgC,YAAY,GAAG,CAAC9B,KAAK,GAAGP,SAAS,GAAGoC,SAArB,IAAkC/B,QAAvD;AAEA,UAAIyC,OAAO,GAAG,CAAd;;AACA,UAAIR,QAAQ,KAAK,CAAjB,EAAoB;AAClB,YAAID,YAAY,GAAGK,aAAnB,EAAkC;AAChCI,UAAAA,OAAO,GAAGrC,SAAV;AACD,SAFD,MAEO,IAAI4B,YAAY,GAAG,CAACM,cAApB,EAAoC;AACzCG,UAAAA,OAAO,GAAG,CAAClC,UAAX;AACD;AACF,OAND,MAMO,IAAI0B,QAAQ,KAAK,CAAjB,EAAoB;AACzB;AACA,YAAID,YAAY,GAAG,CAACK,aAApB,EAAmC;AACjCI,UAAAA,OAAO,GAAGrC,SAAV;AACD;AACF,OALM,MAKA;AACL;AACA,YAAI4B,YAAY,GAAGM,cAAnB,EAAmC;AACjCG,UAAAA,OAAO,GAAG,CAAClC,UAAX;AACD;AACF;;AAED,WAAKmC,UAAL,CAAgBH,YAAhB,EAA8BE,OAA9B,EAAuCV,SAAS,GAAG/B,QAAnD;AACD,KA7KkC;;AAAA,wCA+Kd,CACnB2C,SADmB,EAEnBF,OAFmB,EAGnBV,SAHmB,KAShB;AACH,YAAM;AAAE7B,QAAAA,KAAF;AAASC,QAAAA;AAAT,UAA4B,KAAKJ,KAAvC;AACAG,MAAAA,KAAK,CAAC0C,QAAN,CAAe,CAAf;AACAzC,MAAAA,cAAc,CAACyC,QAAf,CAAwBD,SAAxB;AAEA,WAAKE,QAAL,CAAc;AAAEZ,QAAAA,QAAQ,EAAEzB,IAAI,CAACsC,IAAL,CAAUL,OAAV;AAAZ,OAAd;AACArD,MAAAA,QAAQ,CAAC2D,MAAT,CAAgB5C,cAAhB,EAAgC;AAC9B6C,QAAAA,kBAAkB,EAAE,GADU;AAE9BC,QAAAA,yBAAyB,EAAE,GAFG;AAG9BC,QAAAA,QAAQ,EAAEnB,SAHoB;AAI9BoB,QAAAA,UAAU,EAAE,CAJkB;AAK9BV,QAAAA,OAL8B;AAM9BW,QAAAA,eAAe,EAAE,KAAKtD,KAAL,CAAWuD,mBANE;AAO9B,WAAG,KAAKvD,KAAL,CAAWwD;AAPgB,OAAhC,EAQGC,KARH,CAQS,CAAC;AAAEC,QAAAA;AAAF,OAAD,KAAkB;AACzB,YAAIA,QAAJ,EAAc;AACZ,cAAIf,OAAO,GAAG,CAAd,EAAiB;AAAA;;AACf,2DAAK3C,KAAL,EAAW2D,mBAAX;AACA,2DAAK3D,KAAL,EAAW4D,eAAX,qGAA6B,MAA7B,EAAqC,IAArC;AACD,WAHD,MAGO,IAAIjB,OAAO,GAAG,CAAd,EAAiB;AAAA;;AACtB,2DAAK3C,KAAL,EAAW6D,oBAAX;AACA,2DAAK7D,KAAL,EAAW4D,eAAX,qGAA6B,OAA7B,EAAsC,IAAtC;AACD,WAHM,MAGA;AAAA;;AACL,kBAAME,gBAAgB,GAAGjB,SAAS,GAAG,CAAZ,GAAgB,MAAhB,GAAyB,OAAlD;AACA,2DAAK7C,KAAL,EAAW+D,gBAAX,qGAA8BD,gBAA9B,EAAgD,IAAhD;AACD;AACF;AACF,OArBD;;AAsBA,UAAInB,OAAO,GAAG,CAAd,EAAiB;AAAA;;AACf,uDAAK3C,KAAL,EAAWgE,uBAAX;AACA,uDAAKhE,KAAL,EAAWiE,mBAAX,qGAAiC,MAAjC;AACD,OAHD,MAGO,IAAItB,OAAO,GAAG,CAAd,EAAiB;AAAA;;AACtB,yDAAK3C,KAAL,EAAWkE,wBAAX;AACA,yDAAKlE,KAAL,EAAWiE,mBAAX,wGAAiC,OAAjC;AACD,OAHM,MAGA;AAAA;;AACL,cAAMH,gBAAgB,GAAGjB,SAAS,GAAG,CAAZ,GAAgB,MAAhB,GAAyB,OAAlD;AACA,yDAAK7C,KAAL,EAAWmE,oBAAX,wGAAkCL,gBAAlC;AACD;AACF,KA9NkC;;AAAA,yCAgOb,CAAC;AAAEnC,MAAAA;AAAF,KAAD,KAAwC;AAC5D,WAAKoB,QAAL,CAAc;AAAExC,QAAAA,QAAQ,EAAEoB,WAAW,CAACyC,MAAZ,CAAmBC;AAA/B,OAAd;AACD,KAlOkC;;AAAA,2CAoOX,MAAM;AAC5B,YAAM;AAAE/D,QAAAA,SAAS,GAAG,CAAd;AAAiBC,QAAAA,QAAQ,GAAG,CAA5B;AAA+B4B,QAAAA;AAA/B,UAA4C,KAAKlC,KAAvD;AACA,YAAM;AAAEO,QAAAA,WAAW,GAAGD;AAAhB,UAA6B,KAAKN,KAAxC;AACA,YAAMQ,UAAU,GAAGF,QAAQ,GAAGC,WAA9B;;AACA,UAAI2B,QAAQ,KAAK,CAAjB,EAAoB;AAClB,eAAO7B,SAAP;AACD,OAFD,MAEO,IAAI6B,QAAQ,KAAK,CAAC,CAAlB,EAAqB;AAC1B,eAAO,CAAC1B,UAAR;AACD;;AACD,aAAO,CAAP;AACD,KA9OkC;;AAAA,mCAgP3B,MAAM;AACZ,WAAKmC,UAAL,CAAgB,KAAKF,aAAL,EAAhB,EAAsC,CAAtC;AACD,KAlPkC;;AAAA,sCAoPxB,MAAM;AACf,YAAM;AAAEpC,QAAAA,SAAS,GAAG;AAAd,UAAoB,KAAKL,KAA/B;AACA,WAAK2C,UAAL,CAAgB,KAAKF,aAAL,EAAhB,EAAsCpC,SAAtC;AACD,KAvPkC;;AAAA,uCAyPvB,MAAM;AAChB,YAAM;AAAEC,QAAAA,QAAQ,GAAG;AAAb,UAAmB,KAAKN,KAA9B;AACA,YAAM;AAAEO,QAAAA,WAAW,GAAGD;AAAhB,UAA6B,KAAKN,KAAxC;AACA,YAAMQ,UAAU,GAAGF,QAAQ,GAAGC,WAA9B;AACA,WAAKoC,UAAL,CAAgB,KAAKF,aAAL,EAAhB,EAAsC,CAACjC,UAAvC;AACD,KA9PkC;;AAAA,mCAgQ3B,MAAM;AACZ,YAAM;AAAEL,QAAAA,KAAF;AAASC,QAAAA;AAAT,UAA4B,KAAKJ,KAAvC;AACAG,MAAAA,KAAK,CAAC0C,QAAN,CAAe,CAAf;AACAzC,MAAAA,cAAc,CAACyC,QAAf,CAAwB,CAAxB;AACA,WAAKC,QAAL,CAAc;AAAEZ,QAAAA,QAAQ,EAAE;AAAZ,OAAd;AACD,KArQkC;;AAEjC,UAAM/B,MAAK,GAAG,IAAId,QAAQ,CAAC8B,KAAb,CAAmB,CAAnB,CAAd;;AACA,SAAKnB,KAAL,GAAa;AACXG,MAAAA,KAAK,EAALA,MADW;AAEXC,MAAAA,cAAc,EAAE,IAAIf,QAAQ,CAAC8B,KAAb,CAAmB,CAAnB,CAFL;AAGXe,MAAAA,QAAQ,EAAE,CAHC;AAIX7B,MAAAA,SAAS,EAAEgE,SAJA;AAKX9D,MAAAA,WAAW,EAAE8D,SALF;AAMX/D,MAAAA,QAAQ,EAAE+D;AANC,KAAb;AAQA,SAAKC,mBAAL,CAAyBvE,MAAzB,EAAgC,KAAKC,KAArC;AAEA,SAAKuE,cAAL,GAAsBlF,QAAQ,CAACmF,KAAT,CACpB,CAAC;AAAE9C,MAAAA,WAAW,EAAE;AAAEO,QAAAA,YAAY,EAAE9B;AAAhB;AAAf,KAAD,CADoB,EAEpB;AAAEkD,MAAAA,eAAe,EAAEtD,MAAK,CAACuD;AAAzB,KAFoB,CAAtB;AAID;;AAEDmB,EAAAA,qBAAqB,CAAC1E,KAAD,EAAwBC,KAAxB,EAA+C;AAClE,QACE,KAAKD,KAAL,CAAWE,QAAX,KAAwBF,KAAK,CAACE,QAA9B,IACA,KAAKF,KAAL,CAAWY,aAAX,KAA6BZ,KAAK,CAACY,aADnC,IAEA,KAAKZ,KAAL,CAAWa,cAAX,KAA8Bb,KAAK,CAACa,cAFpC,IAGA,KAAKb,KAAL,CAAWG,iBAAX,KAAiCH,KAAK,CAACG,iBAHvC,IAIA,KAAKF,KAAL,CAAWK,SAAX,KAAyBL,KAAK,CAACK,SAJ/B,IAKA,KAAKL,KAAL,CAAWO,WAAX,KAA2BP,KAAK,CAACO,WALjC,IAMA,KAAKP,KAAL,CAAWM,QAAX,KAAwBN,KAAK,CAACM,QAPhC,EAQE;AACA,WAAKgE,mBAAL,CAAyBvE,KAAzB,EAAgCC,KAAhC;AACD;;AAED,WAAO,IAAP;AACD;;AAsOD0E,EAAAA,MAAM,GAAG;AACP,UAAM;AAAExC,MAAAA;AAAF,QAAe,KAAKlC,KAA1B;AACA,UAAM;AACJ2E,MAAAA,QADI;AAEJC,MAAAA,iBAFI;AAGJC,MAAAA,kBAHI;AAIJC,MAAAA,sBAAsB,GAAG,EAJrB;AAKJC,MAAAA,uBAAuB,GAAG;AALtB,QAMF,KAAKhF,KANT;AAQA,UAAMiF,IAAI,GAAGJ,iBAAiB,iBAC5B,oBAAC,QAAD,CAAU,IAAV;AACE,MAAA,KAAK,EAAE,CACLK,MAAM,CAACC,WADF,EAEL;AACA;AACA;AACA;AAAEC,QAAAA,SAAS,EAAE,CAAC;AAAEC,UAAAA,UAAU,EAAE,KAAKhE;AAAnB,SAAD;AAAb,OALK;AADT,OAQGwD,iBAAiB,CAAC,KAAK1D,cAAN,EAAuB,KAAKL,MAA5B,EAAqC,IAArC,CARpB,eASE,oBAAC,IAAD;AACE,MAAA,QAAQ,EAAE,CAAC;AAAEa,QAAAA;AAAF,OAAD,KACR,KAAKoB,QAAL,CAAc;AAAEzC,QAAAA,SAAS,EAAEqB,WAAW,CAACyC,MAAZ,CAAmBkB;AAAhC,OAAd;AAFJ,MATF,CADF;AAkBA,UAAMC,KAAK,GAAGT,kBAAkB,iBAC9B,oBAAC,QAAD,CAAU,IAAV;AACE,MAAA,KAAK,EAAE,CACLI,MAAM,CAACM,YADF,EAEL;AAAEJ,QAAAA,SAAS,EAAE,CAAC;AAAEC,UAAAA,UAAU,EAAE,KAAK3D;AAAnB,SAAD;AAAb,OAFK;AADT,OAKGoD,kBAAkB,CAAC,KAAKrD,eAAN,EAAwB,KAAKX,MAA7B,EAAsC,IAAtC,CALrB,eAME,oBAAC,IAAD;AACE,MAAA,QAAQ,EAAE,CAAC;AAAEa,QAAAA;AAAF,OAAD,KACR,KAAKoB,QAAL,CAAc;AAAEvC,QAAAA,WAAW,EAAEmB,WAAW,CAACyC,MAAZ,CAAmBkB;AAAlC,OAAd;AAFJ,MANF,CADF;AAeA,wBACE,oBAAC,iBAAD;AACE,MAAA,aAAa,EAAE,CAAC,CAACN,uBAAF,EAA2BD,sBAA3B,CADjB;AAEE,MAAA,WAAW,EAAC;AAFd,OAGM,KAAK/E,KAHX;AAIE,MAAA,cAAc,EAAE,KAAKwE,cAJvB;AAKE,MAAA,oBAAoB,EAAE,KAAKiB;AAL7B,qBAME,oBAAC,QAAD,CAAU,IAAV;AACE,MAAA,QAAQ,EAAE,KAAKC,WADjB;AAEE,MAAA,KAAK,EAAE,CAACR,MAAM,CAACS,SAAR,EAAmB,KAAK3F,KAAL,CAAW4F,cAA9B;AAFT,OAGGX,IAHH,EAIGM,KAJH,eAKE,oBAAC,iBAAD;AACE,MAAA,OAAO,EAAEpD,QAAQ,KAAK,CADxB;AAEE,MAAA,WAAW,EAAC,OAFd;AAGE,MAAA,oBAAoB,EAAE,KAAK0D;AAH7B,oBAIE,oBAAC,QAAD,CAAU,IAAV;AACE,MAAA,aAAa,EAAE1D,QAAQ,KAAK,CAAb,GAAiB,MAAjB,GAA0B,UAD3C;AAEE,MAAA,KAAK,EAAE,CACL;AACEiD,QAAAA,SAAS,EAAE,CAAC;AAAEC,UAAAA,UAAU,EAAE,KAAKvE;AAAnB,SAAD;AADb,OADK,EAIL,KAAKd,KAAL,CAAW8F,sBAJN;AAFT,OAQGlB,QARH,CAJF,CALF,CANF,CADF;AA8BD;;AAvVD;;gBAHmB9E,S,kBAIG;AACpBI,EAAAA,QAAQ,EAAE,CADU;AAEpBC,EAAAA,iBAAiB,EAAE,CAFC;AAGpBoD,EAAAA,mBAAmB,EAAE;AAHD,C;;AAyVxB,MAAM2B,MAAM,GAAG3F,UAAU,CAACwG,MAAX,CAAkB;AAC/BJ,EAAAA,SAAS,EAAE;AACTK,IAAAA,QAAQ,EAAE;AADD,GADoB;AAI/Bb,EAAAA,WAAW,EAAE,EACX,GAAG5F,UAAU,CAAC0G,kBADH;AAEXC,IAAAA,aAAa,EAAEzG,WAAW,CAAC0G,KAAZ,GAAoB,aAApB,GAAoC;AAFxC,GAJkB;AAQ/BX,EAAAA,YAAY,EAAE,EACZ,GAAGjG,UAAU,CAAC0G,kBADF;AAEZC,IAAAA,aAAa,EAAEzG,WAAW,CAAC0G,KAAZ,GAAoB,KAApB,GAA4B;AAF/B;AARiB,CAAlB,CAAf", "sourcesContent": ["// Similarily to the DrawerLayout component this deserves to be put in a\n// separate repo. Although, keeping it here for the time being will allow us to\n// move faster and fix possible issues quicker\n\nimport * as React from 'react';\nimport { Component } from 'react';\nimport {\n  Animated,\n  StyleSheet,\n  View,\n  I18nManager,\n  LayoutChangeEvent,\n  StyleProp,\n  ViewStyle,\n} from 'react-native';\n\nimport {\n  GestureEvent,\n  HandlerStateChangeEvent,\n} from '../handlers/gestureHandlerCommon';\nimport {\n  PanGestureHandler,\n  PanGestureHandlerProps,\n} from '../handlers/PanGestureHandler';\nimport {\n  PanGestureHandlerEventPayload,\n  TapGestureHandlerEventPayload,\n} from '../handlers/GestureHandlerEventPayload';\nimport { TapGestureHandler } from '../handlers/TapGestureHandler';\nimport { State } from '../State';\n\nconst DRAG_TOSS = 0.05;\n\ntype SwipeableExcludes = Exclude<\n  keyof PanGestureHandlerProps,\n  'onGestureEvent' | 'onHandlerStateChange'\n>;\n\n// Animated.AnimatedInterpolation has been converted to a generic type\n// in @types/react-native 0.70. This way we can maintain compatibility\n// with all versions of @types/react-native\ntype AnimatedInterpolation = ReturnType<Animated.Value['interpolate']>;\n\nexport interface SwipeableProps\n  extends Pick<PanGestureHandlerProps, SwipeableExcludes> {\n  /**\n   * Enables two-finger gestures on supported devices, for example iPads with\n   * trackpads. If not enabled the gesture will require click + drag, with\n   * `enableTrackpadTwoFingerGesture` swiping with two fingers will also trigger\n   * the gesture.\n   */\n  enableTrackpadTwoFingerGesture?: boolean;\n\n  /**\n   * Specifies how much the visual interaction will be delayed compared to the\n   * gesture distance. e.g. value of 1 will indicate that the swipeable panel\n   * should exactly follow the gesture, 2 means it is going to be two times\n   * \"slower\".\n   */\n  friction?: number;\n\n  /**\n   * Distance from the left edge at which released panel will animate to the\n   * open state (or the open panel will animate into the closed state). By\n   * default it's a half of the panel's width.\n   */\n  leftThreshold?: number;\n\n  /**\n   * Distance from the right edge at which released panel will animate to the\n   * open state (or the open panel will animate into the closed state). By\n   * default it's a half of the panel's width.\n   */\n  rightThreshold?: number;\n\n  /**\n   * Distance that the panel must be dragged from the left edge to be considered\n   * a swipe. The default value is 10.\n   */\n  dragOffsetFromLeftEdge?: number;\n\n  /**\n   * Distance that the panel must be dragged from the right edge to be considered\n   * a swipe. The default value is 10.\n   */\n  dragOffsetFromRightEdge?: number;\n\n  /**\n   * Value indicating if the swipeable panel can be pulled further than the left\n   * actions panel's width. It is set to true by default as long as the left\n   * panel render method is present.\n   */\n  overshootLeft?: boolean;\n\n  /**\n   * Value indicating if the swipeable panel can be pulled further than the\n   * right actions panel's width. It is set to true by default as long as the\n   * right panel render method is present.\n   */\n  overshootRight?: boolean;\n\n  /**\n   * Specifies how much the visual interaction will be delayed compared to the\n   * gesture distance at overshoot. Default value is 1, it mean no friction, for\n   * a native feel, try 8 or above.\n   */\n  overshootFriction?: number;\n\n  /**\n   * @deprecated Use `direction` argument of onSwipeableOpen()\n   *\n   * Called when left action panel gets open.\n   */\n  onSwipeableLeftOpen?: () => void;\n\n  /**\n   * @deprecated Use `direction` argument of onSwipeableOpen()\n   *\n   * Called when right action panel gets open.\n   */\n  onSwipeableRightOpen?: () => void;\n\n  /**\n   * Called when action panel gets open (either right or left).\n   */\n  onSwipeableOpen?: (direction: 'left' | 'right', swipeable: Swipeable) => void;\n\n  /**\n   * Called when action panel is closed.\n   */\n  onSwipeableClose?: (\n    direction: 'left' | 'right',\n    swipeable: Swipeable\n  ) => void;\n\n  /**\n   * @deprecated Use `direction` argument of onSwipeableWillOpen()\n   *\n   * Called when left action panel starts animating on open.\n   */\n  onSwipeableLeftWillOpen?: () => void;\n\n  /**\n   * @deprecated Use `direction` argument of onSwipeableWillOpen()\n   *\n   * Called when right action panel starts animating on open.\n   */\n  onSwipeableRightWillOpen?: () => void;\n\n  /**\n   * Called when action panel starts animating on open (either right or left).\n   */\n  onSwipeableWillOpen?: (direction: 'left' | 'right') => void;\n\n  /**\n   * Called when action panel starts animating on close.\n   */\n  onSwipeableWillClose?: (direction: 'left' | 'right') => void;\n\n  /**\n   * Called when action panel starts being shown on dragging to open.\n   */\n  onSwipeableOpenStartDrag?: (direction: 'left' | 'right') => void;\n\n  /**\n   * Called when action panel starts being shown on dragging to close.\n   */\n  onSwipeableCloseStartDrag?: (direction: 'left' | 'right') => void;\n\n  /**\n   *\n   * This map describes the values to use as inputRange for extra interpolation:\n   * AnimatedValue: [startValue, endValue]\n   *\n   * progressAnimatedValue: [0, 1] dragAnimatedValue: [0, +]\n   *\n   * To support `rtl` flexbox layouts use `flexDirection` styling.\n   * */\n  renderLeftActions?: (\n    progressAnimatedValue: AnimatedInterpolation,\n    dragAnimatedValue: AnimatedInterpolation,\n    swipeable: Swipeable\n  ) => React.ReactNode;\n  /**\n   *\n   * This map describes the values to use as inputRange for extra interpolation:\n   * AnimatedValue: [startValue, endValue]\n   *\n   * progressAnimatedValue: [0, 1] dragAnimatedValue: [0, -]\n   *\n   * To support `rtl` flexbox layouts use `flexDirection` styling.\n   * */\n  renderRightActions?: (\n    progressAnimatedValue: AnimatedInterpolation,\n    dragAnimatedValue: AnimatedInterpolation,\n    swipeable: Swipeable\n  ) => React.ReactNode;\n\n  useNativeAnimations?: boolean;\n\n  animationOptions?: Record<string, unknown>;\n\n  /**\n   * Style object for the container (`Animated.View`), for example to override\n   * `overflow: 'hidden'`.\n   */\n  containerStyle?: StyleProp<ViewStyle>;\n\n  /**\n   * Style object for the children container (`Animated.View`), for example to\n   * apply `flex: 1`\n   */\n  childrenContainerStyle?: StyleProp<ViewStyle>;\n}\n\ntype SwipeableState = {\n  dragX: Animated.Value;\n  rowTranslation: Animated.Value;\n  rowState: number;\n  leftWidth?: number;\n  rightOffset?: number;\n  rowWidth?: number;\n};\n\n/**\n * @deprecated use Reanimated version of Swipeable instead\n *\n * This component allows for implementing swipeable rows or similar interaction.\n */\n\nexport default class Swipeable extends Component<\n  SwipeableProps,\n  SwipeableState\n> {\n  static defaultProps = {\n    friction: 1,\n    overshootFriction: 1,\n    useNativeAnimations: true,\n  };\n\n  constructor(props: SwipeableProps) {\n    super(props);\n    const dragX = new Animated.Value(0);\n    this.state = {\n      dragX,\n      rowTranslation: new Animated.Value(0),\n      rowState: 0,\n      leftWidth: undefined,\n      rightOffset: undefined,\n      rowWidth: undefined,\n    };\n    this.updateAnimatedEvent(props, this.state);\n\n    this.onGestureEvent = Animated.event(\n      [{ nativeEvent: { translationX: dragX } }],\n      { useNativeDriver: props.useNativeAnimations! }\n    );\n  }\n\n  shouldComponentUpdate(props: SwipeableProps, state: SwipeableState) {\n    if (\n      this.props.friction !== props.friction ||\n      this.props.overshootLeft !== props.overshootLeft ||\n      this.props.overshootRight !== props.overshootRight ||\n      this.props.overshootFriction !== props.overshootFriction ||\n      this.state.leftWidth !== state.leftWidth ||\n      this.state.rightOffset !== state.rightOffset ||\n      this.state.rowWidth !== state.rowWidth\n    ) {\n      this.updateAnimatedEvent(props, state);\n    }\n\n    return true;\n  }\n\n  private onGestureEvent?: (\n    event: GestureEvent<PanGestureHandlerEventPayload>\n  ) => void;\n  private transX?: AnimatedInterpolation;\n  private showLeftAction?: AnimatedInterpolation | Animated.Value;\n  private leftActionTranslate?: AnimatedInterpolation;\n  private showRightAction?: AnimatedInterpolation | Animated.Value;\n  private rightActionTranslate?: AnimatedInterpolation;\n\n  private updateAnimatedEvent = (\n    props: SwipeableProps,\n    state: SwipeableState\n  ) => {\n    const { friction, overshootFriction } = props;\n    const { dragX, rowTranslation, leftWidth = 0, rowWidth = 0 } = state;\n    const { rightOffset = rowWidth } = state;\n    const rightWidth = Math.max(0, rowWidth - rightOffset);\n\n    const { overshootLeft = leftWidth > 0, overshootRight = rightWidth > 0 } =\n      props;\n\n    const transX = Animated.add(\n      rowTranslation,\n      dragX.interpolate({\n        inputRange: [0, friction!],\n        outputRange: [0, 1],\n      })\n    ).interpolate({\n      inputRange: [-rightWidth - 1, -rightWidth, leftWidth, leftWidth + 1],\n      outputRange: [\n        -rightWidth - (overshootRight ? 1 / overshootFriction! : 0),\n        -rightWidth,\n        leftWidth,\n        leftWidth + (overshootLeft ? 1 / overshootFriction! : 0),\n      ],\n    });\n    this.transX = transX;\n    this.showLeftAction =\n      leftWidth > 0\n        ? transX.interpolate({\n            inputRange: [-1, 0, leftWidth],\n            outputRange: [0, 0, 1],\n          })\n        : new Animated.Value(0);\n    this.leftActionTranslate = this.showLeftAction.interpolate({\n      inputRange: [0, Number.MIN_VALUE],\n      outputRange: [-10000, 0],\n      extrapolate: 'clamp',\n    });\n    this.showRightAction =\n      rightWidth > 0\n        ? transX.interpolate({\n            inputRange: [-rightWidth, 0, 1],\n            outputRange: [1, 0, 0],\n          })\n        : new Animated.Value(0);\n    this.rightActionTranslate = this.showRightAction.interpolate({\n      inputRange: [0, Number.MIN_VALUE],\n      outputRange: [-10000, 0],\n      extrapolate: 'clamp',\n    });\n  };\n\n  private onTapHandlerStateChange = ({\n    nativeEvent,\n  }: HandlerStateChangeEvent<TapGestureHandlerEventPayload>) => {\n    if (nativeEvent.oldState === State.ACTIVE) {\n      this.close();\n    }\n  };\n\n  private onHandlerStateChange = (\n    ev: HandlerStateChangeEvent<PanGestureHandlerEventPayload>\n  ) => {\n    if (ev.nativeEvent.oldState === State.ACTIVE) {\n      this.handleRelease(ev);\n    }\n\n    if (ev.nativeEvent.state === State.ACTIVE) {\n      const { velocityX, translationX: dragX } = ev.nativeEvent;\n      const { rowState } = this.state;\n      const { friction } = this.props;\n\n      const translationX = (dragX + DRAG_TOSS * velocityX) / friction!;\n\n      const direction =\n        rowState === -1\n          ? 'right'\n          : rowState === 1\n          ? 'left'\n          : translationX > 0\n          ? 'left'\n          : 'right';\n\n      if (rowState === 0) {\n        this.props.onSwipeableOpenStartDrag?.(direction);\n      } else {\n        this.props.onSwipeableCloseStartDrag?.(direction);\n      }\n    }\n  };\n\n  private handleRelease = (\n    ev: HandlerStateChangeEvent<PanGestureHandlerEventPayload>\n  ) => {\n    const { velocityX, translationX: dragX } = ev.nativeEvent;\n    const { leftWidth = 0, rowWidth = 0, rowState } = this.state;\n    const { rightOffset = rowWidth } = this.state;\n    const rightWidth = rowWidth - rightOffset;\n    const {\n      friction,\n      leftThreshold = leftWidth / 2,\n      rightThreshold = rightWidth / 2,\n    } = this.props;\n\n    const startOffsetX = this.currentOffset() + dragX / friction!;\n    const translationX = (dragX + DRAG_TOSS * velocityX) / friction!;\n\n    let toValue = 0;\n    if (rowState === 0) {\n      if (translationX > leftThreshold) {\n        toValue = leftWidth;\n      } else if (translationX < -rightThreshold) {\n        toValue = -rightWidth;\n      }\n    } else if (rowState === 1) {\n      // Swiped to left\n      if (translationX > -leftThreshold) {\n        toValue = leftWidth;\n      }\n    } else {\n      // Swiped to right\n      if (translationX < rightThreshold) {\n        toValue = -rightWidth;\n      }\n    }\n\n    this.animateRow(startOffsetX, toValue, velocityX / friction!);\n  };\n\n  private animateRow = (\n    fromValue: number,\n    toValue: number,\n    velocityX?:\n      | number\n      | {\n          x: number;\n          y: number;\n        }\n  ) => {\n    const { dragX, rowTranslation } = this.state;\n    dragX.setValue(0);\n    rowTranslation.setValue(fromValue);\n\n    this.setState({ rowState: Math.sign(toValue) });\n    Animated.spring(rowTranslation, {\n      restSpeedThreshold: 1.7,\n      restDisplacementThreshold: 0.4,\n      velocity: velocityX,\n      bounciness: 0,\n      toValue,\n      useNativeDriver: this.props.useNativeAnimations!,\n      ...this.props.animationOptions,\n    }).start(({ finished }) => {\n      if (finished) {\n        if (toValue > 0) {\n          this.props.onSwipeableLeftOpen?.();\n          this.props.onSwipeableOpen?.('left', this);\n        } else if (toValue < 0) {\n          this.props.onSwipeableRightOpen?.();\n          this.props.onSwipeableOpen?.('right', this);\n        } else {\n          const closingDirection = fromValue > 0 ? 'left' : 'right';\n          this.props.onSwipeableClose?.(closingDirection, this);\n        }\n      }\n    });\n    if (toValue > 0) {\n      this.props.onSwipeableLeftWillOpen?.();\n      this.props.onSwipeableWillOpen?.('left');\n    } else if (toValue < 0) {\n      this.props.onSwipeableRightWillOpen?.();\n      this.props.onSwipeableWillOpen?.('right');\n    } else {\n      const closingDirection = fromValue > 0 ? 'left' : 'right';\n      this.props.onSwipeableWillClose?.(closingDirection);\n    }\n  };\n\n  private onRowLayout = ({ nativeEvent }: LayoutChangeEvent) => {\n    this.setState({ rowWidth: nativeEvent.layout.width });\n  };\n\n  private currentOffset = () => {\n    const { leftWidth = 0, rowWidth = 0, rowState } = this.state;\n    const { rightOffset = rowWidth } = this.state;\n    const rightWidth = rowWidth - rightOffset;\n    if (rowState === 1) {\n      return leftWidth;\n    } else if (rowState === -1) {\n      return -rightWidth;\n    }\n    return 0;\n  };\n\n  close = () => {\n    this.animateRow(this.currentOffset(), 0);\n  };\n\n  openLeft = () => {\n    const { leftWidth = 0 } = this.state;\n    this.animateRow(this.currentOffset(), leftWidth);\n  };\n\n  openRight = () => {\n    const { rowWidth = 0 } = this.state;\n    const { rightOffset = rowWidth } = this.state;\n    const rightWidth = rowWidth - rightOffset;\n    this.animateRow(this.currentOffset(), -rightWidth);\n  };\n\n  reset = () => {\n    const { dragX, rowTranslation } = this.state;\n    dragX.setValue(0);\n    rowTranslation.setValue(0);\n    this.setState({ rowState: 0 });\n  };\n\n  render() {\n    const { rowState } = this.state;\n    const {\n      children,\n      renderLeftActions,\n      renderRightActions,\n      dragOffsetFromLeftEdge = 10,\n      dragOffsetFromRightEdge = 10,\n    } = this.props;\n\n    const left = renderLeftActions && (\n      <Animated.View\n        style={[\n          styles.leftActions,\n          // All those and below parameters can have ! since they are all\n          // asigned in constructor in `updateAnimatedEvent` but TS cannot spot\n          // it for some reason\n          { transform: [{ translateX: this.leftActionTranslate! }] },\n        ]}>\n        {renderLeftActions(this.showLeftAction!, this.transX!, this)}\n        <View\n          onLayout={({ nativeEvent }) =>\n            this.setState({ leftWidth: nativeEvent.layout.x })\n          }\n        />\n      </Animated.View>\n    );\n\n    const right = renderRightActions && (\n      <Animated.View\n        style={[\n          styles.rightActions,\n          { transform: [{ translateX: this.rightActionTranslate! }] },\n        ]}>\n        {renderRightActions(this.showRightAction!, this.transX!, this)}\n        <View\n          onLayout={({ nativeEvent }) =>\n            this.setState({ rightOffset: nativeEvent.layout.x })\n          }\n        />\n      </Animated.View>\n    );\n\n    return (\n      <PanGestureHandler\n        activeOffsetX={[-dragOffsetFromRightEdge, dragOffsetFromLeftEdge]}\n        touchAction=\"pan-y\"\n        {...this.props}\n        onGestureEvent={this.onGestureEvent}\n        onHandlerStateChange={this.onHandlerStateChange}>\n        <Animated.View\n          onLayout={this.onRowLayout}\n          style={[styles.container, this.props.containerStyle]}>\n          {left}\n          {right}\n          <TapGestureHandler\n            enabled={rowState !== 0}\n            touchAction=\"pan-y\"\n            onHandlerStateChange={this.onTapHandlerStateChange}>\n            <Animated.View\n              pointerEvents={rowState === 0 ? 'auto' : 'box-only'}\n              style={[\n                {\n                  transform: [{ translateX: this.transX! }],\n                },\n                this.props.childrenContainerStyle,\n              ]}>\n              {children}\n            </Animated.View>\n          </TapGestureHandler>\n        </Animated.View>\n      </PanGestureHandler>\n    );\n  }\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    overflow: 'hidden',\n  },\n  leftActions: {\n    ...StyleSheet.absoluteFillObject,\n    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',\n  },\n  rightActions: {\n    ...StyleSheet.absoluteFillObject,\n    flexDirection: I18nManager.isRTL ? 'row' : 'row-reverse',\n  },\n});\n"]}