{"version": 3, "sources": ["RNGestureHandlerModule.ts"], "names": ["<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;AAGA;;;;AAHA;AACA;eAGeA,qC", "sourcesContent": ["// Reexport the native module spec used by codegen. The relevant files are inluded on Android\n// to ensure the compatibility with the old arch, while iOS doesn't require those at all.\n\nimport Module from './specs/NativeRNGestureHandlerModule';\nexport default Module;\n"]}