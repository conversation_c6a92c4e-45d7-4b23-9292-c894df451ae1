{"version": 3, "sources": ["GestureHandlerOrchestrator.ts"], "names": ["GestureHandlerOrchestrator", "constructor", "Set", "scheduleFinishedHandlersCleanup", "handlingChangeSemaphore", "cleanupFinishedHandlers", "<PERSON><PERSON><PERSON><PERSON>", "handler", "reset", "setActive", "setAwaiting", "setActivationIndex", "Number", "MAX_VALUE", "removeHandlerFromOrchestrator", "indexInGestureHandlers", "gestureHandlers", "indexOf", "indexInAwaitingHandlers", "awaitingHandlers", "splice", "awaitingHandlersTags", "delete", "getTag", "handlersToRemove", "i", "length", "isFinished", "getState", "isAwaiting", "add", "filter", "has", "hasOtherHandlerToWaitFor", "hasToWaitFor", "<PERSON><PERSON><PERSON><PERSON>", "shouldHandlerWaitForOther", "some", "shouldBeCancelledByFinishedHandler", "shouldBeCancelled", "State", "END", "tryActivate", "cancel", "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handlerState", "CANCELLED", "FAILED", "shouldActivate", "makeActive", "ACTIVE", "fail", "BEGAN", "shouldBeCancelledBy", "shouldHandlerBeCancelledBy", "cleanupAwaitingHandlers", "should<PERSON>ait", "onHandlerStateChange", "newState", "oldState", "sendIfDisabled", "isEnabled", "sendEvent", "isActive", "UNDETERMINED", "includes", "currentState", "setShouldResetProgress", "activationIndex", "push", "recordHandlerIfNotPresent", "MAX_SAFE_INTEGER", "shouldWaitForHandlerFailure", "shouldRequireToWaitForFailure", "canRunSimultaneously", "gh1", "gh2", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "handlerPointers", "getTrackedPointersID", "otherPointers", "PointerTracker", "shareCommonPointers", "getDelegate", "get<PERSON>iew", "checkOverlap", "isPointerWithinBothBounds", "pointer", "point", "getTracker", "getLastAbsoluteCoords", "isPointerInBounds", "state", "cancelMouseAndPenGestures", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "getPointerType", "PointerType", "MOUSE", "STYLUS", "resetTracker", "getInstance", "instance"], "mappings": ";;;;;;;AAAA;;AACA;;AAGA;;;;;;AAEe,MAAMA,0BAAN,CAAiC;AAU9C;AACA;AACQC,EAAAA,WAAW,GAAG;AAAA,6CATuB,EASvB;;AAAA,8CARwB,EAQxB;;AAAA,kDAPsB,IAAIC,GAAJ,EAOtB;;AAAA,qDALY,CAKZ;;AAAA,6CAJI,CAIJ;AAAE;;AAEhBC,EAAAA,+BAA+B,GAAS;AAC9C,QAAI,KAAKC,uBAAL,KAAiC,CAArC,EAAwC;AACtC,WAAKC,uBAAL;AACD;AACF;;AAEOC,EAAAA,YAAY,CAACC,OAAD,EAAiC;AACnDA,IAAAA,OAAO,CAACC,KAAR;AACAD,IAAAA,OAAO,CAACE,SAAR,CAAkB,KAAlB;AACAF,IAAAA,OAAO,CAACG,WAAR,CAAoB,KAApB;AACAH,IAAAA,OAAO,CAACI,kBAAR,CAA2BC,MAAM,CAACC,SAAlC;AACD;;AAEMC,EAAAA,6BAA6B,CAACP,OAAD,EAAiC;AACnE,UAAMQ,sBAAsB,GAAG,KAAKC,eAAL,CAAqBC,OAArB,CAA6BV,OAA7B,CAA/B;AACA,UAAMW,uBAAuB,GAAG,KAAKC,gBAAL,CAAsBF,OAAtB,CAA8BV,OAA9B,CAAhC;;AAEA,QAAIQ,sBAAsB,IAAI,CAA9B,EAAiC;AAC/B,WAAKC,eAAL,CAAqBI,MAArB,CAA4BL,sBAA5B,EAAoD,CAApD;AACD;;AAED,QAAIG,uBAAuB,IAAI,CAA/B,EAAkC;AAChC,WAAKC,gBAAL,CAAsBC,MAAtB,CAA6BF,uBAA7B,EAAsD,CAAtD;AACA,WAAKG,oBAAL,CAA0BC,MAA1B,CAAiCf,OAAO,CAACgB,MAAR,EAAjC;AACD;AACF;;AAEOlB,EAAAA,uBAAuB,GAAS;AACtC,UAAMmB,gBAAgB,GAAG,IAAItB,GAAJ,EAAzB;;AAEA,SAAK,IAAIuB,CAAC,GAAG,KAAKT,eAAL,CAAqBU,MAArB,GAA8B,CAA3C,EAA8CD,CAAC,IAAI,CAAnD,EAAsD,EAAEA,CAAxD,EAA2D;AACzD,YAAMlB,OAAO,GAAG,KAAKS,eAAL,CAAqBS,CAArB,CAAhB;;AAEA,UAAI,KAAKE,UAAL,CAAgBpB,OAAO,CAACqB,QAAR,EAAhB,KAAuC,CAACrB,OAAO,CAACsB,UAAR,EAA5C,EAAkE;AAChE,aAAKvB,YAAL,CAAkBC,OAAlB;AACAiB,QAAAA,gBAAgB,CAACM,GAAjB,CAAqBvB,OAArB;AACD;AACF;;AAED,SAAKS,eAAL,GAAuB,KAAKA,eAAL,CAAqBe,MAArB,CACpBxB,OAAD,IAAa,CAACiB,gBAAgB,CAACQ,GAAjB,CAAqBzB,OAArB,CADO,CAAvB;AAGD;;AAEO0B,EAAAA,wBAAwB,CAAC1B,OAAD,EAAoC;AAClE,UAAM2B,YAAY,GAAIC,YAAD,IAAmC;AACtD,aACE,CAAC,KAAKR,UAAL,CAAgBQ,YAAY,CAACP,QAAb,EAAhB,CAAD,IACA,KAAKQ,yBAAL,CAA+B7B,OAA/B,EAAwC4B,YAAxC,CAFF;AAID,KALD;;AAOA,WAAO,KAAKnB,eAAL,CAAqBqB,IAArB,CAA0BH,YAA1B,CAAP;AACD;;AAEOI,EAAAA,kCAAkC,CACxC/B,OADwC,EAE/B;AACT,UAAMgC,iBAAiB,GAAIJ,YAAD,IAAmC;AAC3D,aACE,KAAKC,yBAAL,CAA+B7B,OAA/B,EAAwC4B,YAAxC,KACAA,YAAY,CAACP,QAAb,OAA4BY,aAAMC,GAFpC;AAID,KALD;;AAOA,WAAO,KAAKzB,eAAL,CAAqBqB,IAArB,CAA0BE,iBAA1B,CAAP;AACD;;AAEOG,EAAAA,WAAW,CAACnC,OAAD,EAAiC;AAClD,QAAI,KAAK+B,kCAAL,CAAwC/B,OAAxC,CAAJ,EAAsD;AACpDA,MAAAA,OAAO,CAACoC,MAAR;AACA;AACD;;AAED,QAAI,KAAKV,wBAAL,CAA8B1B,OAA9B,CAAJ,EAA4C;AAC1C,WAAKqC,kBAAL,CAAwBrC,OAAxB;AACA;AACD;;AAED,UAAMsC,YAAY,GAAGtC,OAAO,CAACqB,QAAR,EAArB;;AAEA,QAAIiB,YAAY,KAAKL,aAAMM,SAAvB,IAAoCD,YAAY,KAAKL,aAAMO,MAA/D,EAAuE;AACrE;AACD;;AAED,QAAI,KAAKC,cAAL,CAAoBzC,OAApB,CAAJ,EAAkC;AAChC,WAAK0C,UAAL,CAAgB1C,OAAhB;AACA;AACD;;AAED,QAAIsC,YAAY,KAAKL,aAAMU,MAA3B,EAAmC;AACjC3C,MAAAA,OAAO,CAAC4C,IAAR;AACA;AACD;;AAED,QAAIN,YAAY,KAAKL,aAAMY,KAA3B,EAAkC;AAChC7C,MAAAA,OAAO,CAACoC,MAAR;AACD;AACF;;AAEOK,EAAAA,cAAc,CAACzC,OAAD,EAAoC;AACxD,UAAM8C,mBAAmB,GAAIlB,YAAD,IAAmC;AAC7D,aAAO,KAAKmB,0BAAL,CAAgC/C,OAAhC,EAAyC4B,YAAzC,CAAP;AACD,KAFD;;AAIA,WAAO,CAAC,KAAKnB,eAAL,CAAqBqB,IAArB,CAA0BgB,mBAA1B,CAAR;AACD;;AAEOE,EAAAA,uBAAuB,CAAChD,OAAD,EAAiC;AAC9D,UAAMiD,UAAU,GAAIrB,YAAD,IAAmC;AACpD,aACE,CAACA,YAAY,CAACN,UAAb,EAAD,IACA,KAAKO,yBAAL,CAA+BD,YAA/B,EAA6C5B,OAA7C,CAFF;AAID,KALD;;AAOA,SAAK,MAAM4B,YAAX,IAA2B,KAAKhB,gBAAhC,EAAkD;AAChD,UAAIqC,UAAU,CAACrB,YAAD,CAAd,EAA8B;AAC5B,aAAK7B,YAAL,CAAkB6B,YAAlB;AACA,aAAKd,oBAAL,CAA0BC,MAA1B,CAAiCa,YAAY,CAACZ,MAAb,EAAjC;AACD;AACF;;AAED,SAAKJ,gBAAL,GAAwB,KAAKA,gBAAL,CAAsBY,MAAtB,CAA8BI,YAAD,IACnD,KAAKd,oBAAL,CAA0BW,GAA1B,CAA8BG,YAAY,CAACZ,MAAb,EAA9B,CADsB,CAAxB;AAGD;;AAEMkC,EAAAA,oBAAoB,CACzBlD,OADyB,EAEzBmD,QAFyB,EAGzBC,QAHyB,EAIzBC,cAJyB,EAKnB;AACN,QAAI,CAACrD,OAAO,CAACsD,SAAR,EAAD,IAAwB,CAACD,cAA7B,EAA6C;AAC3C;AACD;;AAED,SAAKxD,uBAAL,IAAgC,CAAhC;;AAEA,QAAI,KAAKuB,UAAL,CAAgB+B,QAAhB,CAAJ,EAA+B;AAC7B,WAAK,MAAMvB,YAAX,IAA2B,KAAKhB,gBAAhC,EAAkD;AAChD,YACE,CAAC,KAAKiB,yBAAL,CAA+BD,YAA/B,EAA6C5B,OAA7C,CAAD,IACA,CAAC,KAAKc,oBAAL,CAA0BW,GAA1B,CAA8BG,YAAY,CAACZ,MAAb,EAA9B,CAFH,EAGE;AACA;AACD;;AAED,YAAImC,QAAQ,KAAKlB,aAAMC,GAAvB,EAA4B;AAC1B,eAAKC,WAAL,CAAiBP,YAAjB;AACA;AACD;;AAEDA,QAAAA,YAAY,CAACQ,MAAb;;AAEA,YAAIR,YAAY,CAACP,QAAb,OAA4BY,aAAMC,GAAtC,EAA2C;AACzC;AACA;AACA;AACA;AACAN,UAAAA,YAAY,CAAC2B,SAAb,CAAuBtB,aAAMM,SAA7B,EAAwCN,aAAMY,KAA9C;AACD;;AAEDjB,QAAAA,YAAY,CAACzB,WAAb,CAAyB,KAAzB;AACD;AACF;;AAED,QAAIgD,QAAQ,KAAKlB,aAAMU,MAAvB,EAA+B;AAC7B,WAAKR,WAAL,CAAiBnC,OAAjB;AACD,KAFD,MAEO,IAAIoD,QAAQ,KAAKnB,aAAMU,MAAnB,IAA6BS,QAAQ,KAAKnB,aAAMC,GAApD,EAAyD;AAC9D,UAAIlC,OAAO,CAACwD,QAAR,EAAJ,EAAwB;AACtBxD,QAAAA,OAAO,CAACuD,SAAR,CAAkBJ,QAAlB,EAA4BC,QAA5B;AACD,OAFD,MAEO,IACLA,QAAQ,KAAKnB,aAAMU,MAAnB,KACCQ,QAAQ,KAAKlB,aAAMM,SAAnB,IAAgCY,QAAQ,KAAKlB,aAAMO,MADpD,CADK,EAGL;AACAxC,QAAAA,OAAO,CAACuD,SAAR,CAAkBJ,QAAlB,EAA4BlB,aAAMY,KAAlC;AACD;AACF,KATM,MASA,IACLO,QAAQ,KAAKnB,aAAMwB,YAAnB,IACAN,QAAQ,KAAKlB,aAAMM,SAFd,EAGL;AACAvC,MAAAA,OAAO,CAACuD,SAAR,CAAkBJ,QAAlB,EAA4BC,QAA5B;AACD;;AAED,SAAKvD,uBAAL,IAAgC,CAAhC;AAEA,SAAKD,+BAAL;;AAEA,QAAI,CAAC,KAAKgB,gBAAL,CAAsB8C,QAAtB,CAA+B1D,OAA/B,CAAL,EAA8C;AAC5C,WAAKgD,uBAAL,CAA6BhD,OAA7B;AACD;AACF;;AAEO0C,EAAAA,UAAU,CAAC1C,OAAD,EAAiC;AACjD,UAAM2D,YAAY,GAAG3D,OAAO,CAACqB,QAAR,EAArB;AAEArB,IAAAA,OAAO,CAACE,SAAR,CAAkB,IAAlB;AACAF,IAAAA,OAAO,CAAC4D,sBAAR,CAA+B,IAA/B;AACA5D,IAAAA,OAAO,CAACI,kBAAR,CAA2B,KAAKyD,eAAL,EAA3B;;AAEA,SAAK,IAAI3C,CAAC,GAAG,KAAKT,eAAL,CAAqBU,MAArB,GAA8B,CAA3C,EAA8CD,CAAC,IAAI,CAAnD,EAAsD,EAAEA,CAAxD,EAA2D;AACzD,UAAI,KAAK6B,0BAAL,CAAgC,KAAKtC,eAAL,CAAqBS,CAArB,CAAhC,EAAyDlB,OAAzD,CAAJ,EAAuE;AACrE,aAAKS,eAAL,CAAqBS,CAArB,EAAwBkB,MAAxB;AACD;AACF;;AAED,SAAK,MAAMR,YAAX,IAA2B,KAAKhB,gBAAhC,EAAkD;AAChD,UAAI,KAAKmC,0BAAL,CAAgCnB,YAAhC,EAA8C5B,OAA9C,CAAJ,EAA4D;AAC1D4B,QAAAA,YAAY,CAACzB,WAAb,CAAyB,KAAzB;AACD;AACF;;AAEDH,IAAAA,OAAO,CAACuD,SAAR,CAAkBtB,aAAMU,MAAxB,EAAgCV,aAAMY,KAAtC;;AAEA,QAAIc,YAAY,KAAK1B,aAAMU,MAA3B,EAAmC;AACjC3C,MAAAA,OAAO,CAACuD,SAAR,CAAkBtB,aAAMC,GAAxB,EAA6BD,aAAMU,MAAnC;;AACA,UAAIgB,YAAY,KAAK1B,aAAMC,GAA3B,EAAgC;AAC9BlC,QAAAA,OAAO,CAACuD,SAAR,CAAkBtB,aAAMwB,YAAxB,EAAsCxB,aAAMC,GAA5C;AACD;AACF;;AAED,QAAI,CAAClC,OAAO,CAACsB,UAAR,EAAL,EAA2B;AACzB;AACD;;AAEDtB,IAAAA,OAAO,CAACG,WAAR,CAAoB,KAApB;AAEA,SAAKS,gBAAL,GAAwB,KAAKA,gBAAL,CAAsBY,MAAtB,CACrBI,YAAD,IAAkBA,YAAY,KAAK5B,OADb,CAAxB;AAGD;;AAEOqC,EAAAA,kBAAkB,CAACrC,OAAD,EAAiC;AACzD,QAAI,KAAKY,gBAAL,CAAsB8C,QAAtB,CAA+B1D,OAA/B,CAAJ,EAA6C;AAC3C;AACD;;AAED,SAAKY,gBAAL,CAAsBkD,IAAtB,CAA2B9D,OAA3B;AACA,SAAKc,oBAAL,CAA0BS,GAA1B,CAA8BvB,OAAO,CAACgB,MAAR,EAA9B;AAEAhB,IAAAA,OAAO,CAACG,WAAR,CAAoB,IAApB;AACAH,IAAAA,OAAO,CAACI,kBAAR,CAA2B,KAAKyD,eAAL,EAA3B;AACD;;AAEME,EAAAA,yBAAyB,CAAC/D,OAAD,EAAiC;AAC/D,QAAI,KAAKS,eAAL,CAAqBiD,QAArB,CAA8B1D,OAA9B,CAAJ,EAA4C;AAC1C;AACD;;AAED,SAAKS,eAAL,CAAqBqD,IAArB,CAA0B9D,OAA1B;AAEAA,IAAAA,OAAO,CAACE,SAAR,CAAkB,KAAlB;AACAF,IAAAA,OAAO,CAACG,WAAR,CAAoB,KAApB;AACAH,IAAAA,OAAO,CAACI,kBAAR,CAA2BC,MAAM,CAAC2D,gBAAlC;AACD;;AAEOnC,EAAAA,yBAAyB,CAC/B7B,OAD+B,EAE/B4B,YAF+B,EAGtB;AACT,WACE5B,OAAO,KAAK4B,YAAZ,KACC5B,OAAO,CAACiE,2BAAR,CAAoCrC,YAApC,KACCA,YAAY,CAACsC,6BAAb,CAA2ClE,OAA3C,CAFF,CADF;AAKD;;AAEOmE,EAAAA,oBAAoB,CAC1BC,GAD0B,EAE1BC,GAF0B,EAGjB;AACT,WACED,GAAG,KAAKC,GAAR,IACAD,GAAG,CAACE,6BAAJ,CAAkCD,GAAlC,CADA,IAEAA,GAAG,CAACC,6BAAJ,CAAkCF,GAAlC,CAHF;AAKD;;AAEOrB,EAAAA,0BAA0B,CAChC/C,OADgC,EAEhC4B,YAFgC,EAGvB;AACT,QAAI,KAAKuC,oBAAL,CAA0BnE,OAA1B,EAAmC4B,YAAnC,CAAJ,EAAsD;AACpD,aAAO,KAAP;AACD;;AAED,QAAI5B,OAAO,CAACsB,UAAR,MAAwBtB,OAAO,CAACqB,QAAR,OAAuBY,aAAMU,MAAzD,EAAiE;AAC/D;AACA,aAAO3C,OAAO,CAACuE,wBAAR,CAAiC3C,YAAjC,CAAP;AACD;;AAED,UAAM4C,eAAyB,GAAGxE,OAAO,CAACyE,oBAAR,EAAlC;AACA,UAAMC,aAAuB,GAAG9C,YAAY,CAAC6C,oBAAb,EAAhC;;AAEA,QACE,CAACE,wBAAeC,mBAAf,CAAmCJ,eAAnC,EAAoDE,aAApD,CAAD,IACA1E,OAAO,CAAC6E,WAAR,GAAsBC,OAAtB,OAAoClD,YAAY,CAACiD,WAAb,GAA2BC,OAA3B,EAFtC,EAGE;AACA,aAAO,KAAKC,YAAL,CAAkB/E,OAAlB,EAA2B4B,YAA3B,CAAP;AACD;;AAED,WAAO,IAAP;AACD;;AAEOmD,EAAAA,YAAY,CAClB/E,OADkB,EAElB4B,YAFkB,EAGT;AACT;AACA;AACA;AAEA;AAEA,UAAMoD,yBAAyB,GAAIC,OAAD,IAAqB;AACrD,YAAMC,KAAK,GAAGlF,OAAO,CAACmF,UAAR,GAAqBC,qBAArB,CAA2CH,OAA3C,CAAd;AAEA,aACEjF,OAAO,CAAC6E,WAAR,GAAsBQ,iBAAtB,CAAwCH,KAAxC,KACAtD,YAAY,CAACiD,WAAb,GAA2BQ,iBAA3B,CAA6CH,KAA7C,CAFF;AAID,KAPD;;AASA,UAAMV,eAAyB,GAAGxE,OAAO,CAACyE,oBAAR,EAAlC;AACA,UAAMC,aAAuB,GAAG9C,YAAY,CAAC6C,oBAAb,EAAhC;AAEA,WACED,eAAe,CAAC1C,IAAhB,CAAqBkD,yBAArB,KACAN,aAAa,CAAC5C,IAAd,CAAmBkD,yBAAnB,CAFF;AAID;;AAEO5D,EAAAA,UAAU,CAACkE,KAAD,EAAwB;AACxC,WACEA,KAAK,KAAKrD,aAAMC,GAAhB,IAAuBoD,KAAK,KAAKrD,aAAMO,MAAvC,IAAiD8C,KAAK,KAAKrD,aAAMM,SADnE;AAGD,GAhW6C,CAkW9C;AACA;AACA;AACA;AACA;AACA;;;AACOgD,EAAAA,yBAAyB,CAACC,cAAD,EAAwC;AACtE,SAAK/E,eAAL,CAAqBgF,OAArB,CAA8BzF,OAAD,IAA8B;AACzD,UACEA,OAAO,CAAC0F,cAAR,OAA6BC,yBAAYC,KAAzC,IACA5F,OAAO,CAAC0F,cAAR,OAA6BC,yBAAYE,MAF3C,EAGE;AACA;AACD;;AAED,UAAI7F,OAAO,KAAKwF,cAAhB,EAAgC;AAC9BxF,QAAAA,OAAO,CAACoC,MAAR;AACD,OAFD,MAEO;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACApC,QAAAA,OAAO,CAACmF,UAAR,GAAqBW,YAArB;AACD;AACF,KApBD;AAqBD;;AAEwB,SAAXC,WAAW,GAA+B;AACtD,QAAI,CAACtG,0BAA0B,CAACuG,QAAhC,EAA0C;AACxCvG,MAAAA,0BAA0B,CAACuG,QAA3B,GAAsC,IAAIvG,0BAAJ,EAAtC;AACD;;AAED,WAAOA,0BAA0B,CAACuG,QAAlC;AACD;;AAtY6C;;;;gBAA3BvG,0B", "sourcesContent": ["import { PointerType } from '../../PointerType';\nimport { State } from '../../State';\n\nimport type IGestureHandler from '../handlers/IGestureHandler';\nimport PointerTracker from './PointerTracker';\n\nexport default class GestureHandlerOrchestrator {\n  private static instance: GestureHandlerOrchestrator;\n\n  private gestureHandlers: IGestureHandler[] = [];\n  private awaitingHandlers: IGestureHandler[] = [];\n  private awaitingHandlersTags: Set<number> = new Set();\n\n  private handlingChangeSemaphore = 0;\n  private activationIndex = 0;\n\n  // Private beacuse of Singleton\n  // eslint-disable-next-line no-useless-constructor, @typescript-eslint/no-empty-function\n  private constructor() {}\n\n  private scheduleFinishedHandlersCleanup(): void {\n    if (this.handlingChangeSemaphore === 0) {\n      this.cleanupFinishedHandlers();\n    }\n  }\n\n  private cleanHandler(handler: IGestureHandler): void {\n    handler.reset();\n    handler.setActive(false);\n    handler.setAwaiting(false);\n    handler.setActivationIndex(Number.MAX_VALUE);\n  }\n\n  public removeHandlerFromOrchestrator(handler: IGestureHandler): void {\n    const indexInGestureHandlers = this.gestureHandlers.indexOf(handler);\n    const indexInAwaitingHandlers = this.awaitingHandlers.indexOf(handler);\n\n    if (indexInGestureHandlers >= 0) {\n      this.gestureHandlers.splice(indexInGestureHandlers, 1);\n    }\n\n    if (indexInAwaitingHandlers >= 0) {\n      this.awaitingHandlers.splice(indexInAwaitingHandlers, 1);\n      this.awaitingHandlersTags.delete(handler.getTag());\n    }\n  }\n\n  private cleanupFinishedHandlers(): void {\n    const handlersToRemove = new Set<IGestureHandler>();\n\n    for (let i = this.gestureHandlers.length - 1; i >= 0; --i) {\n      const handler = this.gestureHandlers[i];\n\n      if (this.isFinished(handler.getState()) && !handler.isAwaiting()) {\n        this.cleanHandler(handler);\n        handlersToRemove.add(handler);\n      }\n    }\n\n    this.gestureHandlers = this.gestureHandlers.filter(\n      (handler) => !handlersToRemove.has(handler)\n    );\n  }\n\n  private hasOtherHandlerToWaitFor(handler: IGestureHandler): boolean {\n    const hasToWaitFor = (otherHandler: IGestureHandler) => {\n      return (\n        !this.isFinished(otherHandler.getState()) &&\n        this.shouldHandlerWaitForOther(handler, otherHandler)\n      );\n    };\n\n    return this.gestureHandlers.some(hasToWaitFor);\n  }\n\n  private shouldBeCancelledByFinishedHandler(\n    handler: IGestureHandler\n  ): boolean {\n    const shouldBeCancelled = (otherHandler: IGestureHandler) => {\n      return (\n        this.shouldHandlerWaitForOther(handler, otherHandler) &&\n        otherHandler.getState() === State.END\n      );\n    };\n\n    return this.gestureHandlers.some(shouldBeCancelled);\n  }\n\n  private tryActivate(handler: IGestureHandler): void {\n    if (this.shouldBeCancelledByFinishedHandler(handler)) {\n      handler.cancel();\n      return;\n    }\n\n    if (this.hasOtherHandlerToWaitFor(handler)) {\n      this.addAwaitingHandler(handler);\n      return;\n    }\n\n    const handlerState = handler.getState();\n\n    if (handlerState === State.CANCELLED || handlerState === State.FAILED) {\n      return;\n    }\n\n    if (this.shouldActivate(handler)) {\n      this.makeActive(handler);\n      return;\n    }\n\n    if (handlerState === State.ACTIVE) {\n      handler.fail();\n      return;\n    }\n\n    if (handlerState === State.BEGAN) {\n      handler.cancel();\n    }\n  }\n\n  private shouldActivate(handler: IGestureHandler): boolean {\n    const shouldBeCancelledBy = (otherHandler: IGestureHandler) => {\n      return this.shouldHandlerBeCancelledBy(handler, otherHandler);\n    };\n\n    return !this.gestureHandlers.some(shouldBeCancelledBy);\n  }\n\n  private cleanupAwaitingHandlers(handler: IGestureHandler): void {\n    const shouldWait = (otherHandler: IGestureHandler) => {\n      return (\n        !otherHandler.isAwaiting() &&\n        this.shouldHandlerWaitForOther(otherHandler, handler)\n      );\n    };\n\n    for (const otherHandler of this.awaitingHandlers) {\n      if (shouldWait(otherHandler)) {\n        this.cleanHandler(otherHandler);\n        this.awaitingHandlersTags.delete(otherHandler.getTag());\n      }\n    }\n\n    this.awaitingHandlers = this.awaitingHandlers.filter((otherHandler) =>\n      this.awaitingHandlersTags.has(otherHandler.getTag())\n    );\n  }\n\n  public onHandlerStateChange(\n    handler: IGestureHandler,\n    newState: State,\n    oldState: State,\n    sendIfDisabled?: boolean\n  ): void {\n    if (!handler.isEnabled() && !sendIfDisabled) {\n      return;\n    }\n\n    this.handlingChangeSemaphore += 1;\n\n    if (this.isFinished(newState)) {\n      for (const otherHandler of this.awaitingHandlers) {\n        if (\n          !this.shouldHandlerWaitForOther(otherHandler, handler) ||\n          !this.awaitingHandlersTags.has(otherHandler.getTag())\n        ) {\n          continue;\n        }\n\n        if (newState !== State.END) {\n          this.tryActivate(otherHandler);\n          continue;\n        }\n\n        otherHandler.cancel();\n\n        if (otherHandler.getState() === State.END) {\n          // Handle edge case, where discrete gestures end immediately after activation thus\n          // their state is set to END and when the gesture they are waiting for activates they\n          // should be cancelled, however `cancel` was never sent as gestures were already in the END state.\n          // Send synthetic BEGAN -> CANCELLED to properly handle JS logic\n          otherHandler.sendEvent(State.CANCELLED, State.BEGAN);\n        }\n\n        otherHandler.setAwaiting(false);\n      }\n    }\n\n    if (newState === State.ACTIVE) {\n      this.tryActivate(handler);\n    } else if (oldState === State.ACTIVE || oldState === State.END) {\n      if (handler.isActive()) {\n        handler.sendEvent(newState, oldState);\n      } else if (\n        oldState === State.ACTIVE &&\n        (newState === State.CANCELLED || newState === State.FAILED)\n      ) {\n        handler.sendEvent(newState, State.BEGAN);\n      }\n    } else if (\n      oldState !== State.UNDETERMINED ||\n      newState !== State.CANCELLED\n    ) {\n      handler.sendEvent(newState, oldState);\n    }\n\n    this.handlingChangeSemaphore -= 1;\n\n    this.scheduleFinishedHandlersCleanup();\n\n    if (!this.awaitingHandlers.includes(handler)) {\n      this.cleanupAwaitingHandlers(handler);\n    }\n  }\n\n  private makeActive(handler: IGestureHandler): void {\n    const currentState = handler.getState();\n\n    handler.setActive(true);\n    handler.setShouldResetProgress(true);\n    handler.setActivationIndex(this.activationIndex++);\n\n    for (let i = this.gestureHandlers.length - 1; i >= 0; --i) {\n      if (this.shouldHandlerBeCancelledBy(this.gestureHandlers[i], handler)) {\n        this.gestureHandlers[i].cancel();\n      }\n    }\n\n    for (const otherHandler of this.awaitingHandlers) {\n      if (this.shouldHandlerBeCancelledBy(otherHandler, handler)) {\n        otherHandler.setAwaiting(false);\n      }\n    }\n\n    handler.sendEvent(State.ACTIVE, State.BEGAN);\n\n    if (currentState !== State.ACTIVE) {\n      handler.sendEvent(State.END, State.ACTIVE);\n      if (currentState !== State.END) {\n        handler.sendEvent(State.UNDETERMINED, State.END);\n      }\n    }\n\n    if (!handler.isAwaiting()) {\n      return;\n    }\n\n    handler.setAwaiting(false);\n\n    this.awaitingHandlers = this.awaitingHandlers.filter(\n      (otherHandler) => otherHandler !== handler\n    );\n  }\n\n  private addAwaitingHandler(handler: IGestureHandler): void {\n    if (this.awaitingHandlers.includes(handler)) {\n      return;\n    }\n\n    this.awaitingHandlers.push(handler);\n    this.awaitingHandlersTags.add(handler.getTag());\n\n    handler.setAwaiting(true);\n    handler.setActivationIndex(this.activationIndex++);\n  }\n\n  public recordHandlerIfNotPresent(handler: IGestureHandler): void {\n    if (this.gestureHandlers.includes(handler)) {\n      return;\n    }\n\n    this.gestureHandlers.push(handler);\n\n    handler.setActive(false);\n    handler.setAwaiting(false);\n    handler.setActivationIndex(Number.MAX_SAFE_INTEGER);\n  }\n\n  private shouldHandlerWaitForOther(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    return (\n      handler !== otherHandler &&\n      (handler.shouldWaitForHandlerFailure(otherHandler) ||\n        otherHandler.shouldRequireToWaitForFailure(handler))\n    );\n  }\n\n  private canRunSimultaneously(\n    gh1: IGestureHandler,\n    gh2: IGestureHandler\n  ): boolean {\n    return (\n      gh1 === gh2 ||\n      gh1.shouldRecognizeSimultaneously(gh2) ||\n      gh2.shouldRecognizeSimultaneously(gh1)\n    );\n  }\n\n  private shouldHandlerBeCancelledBy(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    if (this.canRunSimultaneously(handler, otherHandler)) {\n      return false;\n    }\n\n    if (handler.isAwaiting() || handler.getState() === State.ACTIVE) {\n      // For now it always returns false\n      return handler.shouldBeCancelledByOther(otherHandler);\n    }\n\n    const handlerPointers: number[] = handler.getTrackedPointersID();\n    const otherPointers: number[] = otherHandler.getTrackedPointersID();\n\n    if (\n      !PointerTracker.shareCommonPointers(handlerPointers, otherPointers) &&\n      handler.getDelegate().getView() !== otherHandler.getDelegate().getView()\n    ) {\n      return this.checkOverlap(handler, otherHandler);\n    }\n\n    return true;\n  }\n\n  private checkOverlap(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    // If handlers don't have common pointers, default return value is false.\n    // However, if at least on pointer overlaps with both handlers, we return true\n    // This solves issue in overlapping parents example\n\n    // TODO: Find better way to handle that issue, for example by activation order and handler cancelling\n\n    const isPointerWithinBothBounds = (pointer: number) => {\n      const point = handler.getTracker().getLastAbsoluteCoords(pointer);\n\n      return (\n        handler.getDelegate().isPointerInBounds(point) &&\n        otherHandler.getDelegate().isPointerInBounds(point)\n      );\n    };\n\n    const handlerPointers: number[] = handler.getTrackedPointersID();\n    const otherPointers: number[] = otherHandler.getTrackedPointersID();\n\n    return (\n      handlerPointers.some(isPointerWithinBothBounds) ||\n      otherPointers.some(isPointerWithinBothBounds)\n    );\n  }\n\n  private isFinished(state: State): boolean {\n    return (\n      state === State.END || state === State.FAILED || state === State.CANCELLED\n    );\n  }\n\n  // This function is called when handler receives touchdown event\n  // If handler is using mouse or pen as a pointer and any handler receives touch event,\n  // mouse/pen event dissappears - it doesn't send onPointerCancel nor onPointerUp (and others)\n  // This became a problem because handler was left at active state without any signal to end or fail\n  // To handle this, when new touch event is received, we loop through active handlers and check which type of\n  // pointer they're using. If there are any handler with mouse/pen as a pointer, we cancel them\n  public cancelMouseAndPenGestures(currentHandler: IGestureHandler): void {\n    this.gestureHandlers.forEach((handler: IGestureHandler) => {\n      if (\n        handler.getPointerType() !== PointerType.MOUSE &&\n        handler.getPointerType() !== PointerType.STYLUS\n      ) {\n        return;\n      }\n\n      if (handler !== currentHandler) {\n        handler.cancel();\n      } else {\n        // Handler that received touch event should have its pointer tracker reset\n        // This allows handler to smoothly change from mouse/pen to touch\n        // The drawback is, that when we try to use mouse/pen one more time, it doesn't send onPointerDown at the first time\n        // so it is required to click two times to get handler to work\n        //\n        // However, handler will receive manually created onPointerEnter that is triggered in EventManager in onPointerMove method.\n        // There may be possibility to use that fact to make handler respond properly to first mouse click\n        handler.getTracker().resetTracker();\n      }\n    });\n  }\n\n  public static getInstance(): GestureHandlerOrchestrator {\n    if (!GestureHandlerOrchestrator.instance) {\n      GestureHandlerOrchestrator.instance = new GestureHandlerOrchestrator();\n    }\n\n    return GestureHandlerOrchestrator.instance;\n  }\n}\n"]}