"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ActionType = void 0;
const ActionType = {
  REANIMATED_WORKLET: 1,
  NATIVE_ANIMATED_EVENT: 2,
  JS_FUNCTION_OLD_API: 3,
  JS_FUNCTION_NEW_API: 4
}; // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value

exports.ActionType = ActionType;
//# sourceMappingURL=ActionType.js.map