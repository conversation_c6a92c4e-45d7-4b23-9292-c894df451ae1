{"version": 3, "sources": ["createNativeWrapper.tsx"], "names": ["React", "useImperativeHandle", "useRef", "NativeViewGestureHandler", "nativeViewProps", "NATIVE_WRAPPER_PROPS_FILTER", "createNativeWrapper", "Component", "config", "ComponentWrapper", "forwardRef", "props", "ref", "gestureHandlerProps", "Object", "keys", "reduce", "res", "key", "<PERSON><PERSON><PERSON><PERSON>", "includes", "_ref", "_gestureHandlerRef", "node", "current", "handlerTag", "displayName", "render", "name"], "mappings": ";;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AACA,SAASC,mBAAT,EAA8BC,MAA9B,QAA4C,OAA5C;AAEA,SACEC,wBADF,EAGEC,eAHF,QAIO,4BAJP;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,2BAA2B,GAAG,CAClC,GAAGD,eAD+B,EAElC,uBAFkC,EAGlC,6BAHkC,CAApC;AAMA,eAAe,SAASE,mBAAT,CACbC,SADa,EAEbC,MAA+C,GAAG,EAFrC,EAGb;AAAA;;AACA,QAAMC,gBAAgB,gBAAGT,KAAK,CAACU,UAAN,CAGvB,CAACC,KAAD,EAAQC,GAAR,KAAgB;AAChB;AACA,UAAMC,mBAAmB,GAAGC,MAAM,CAACC,IAAP,CAAYJ,KAAZ,EAAmBK,MAAnB,CAC1B,CAACC,GAAD,EAAMC,GAAN,KAAc;AACZ;AACA,YAAMC,WAA8B,GAAGd,2BAAvC;;AACA,UAAIc,WAAW,CAACC,QAAZ,CAAqBF,GAArB,CAAJ,EAA+B;AAC7B;AACAD,QAAAA,GAAG,CAACC,GAAD,CAAH,GAAWP,KAAK,CAACO,GAAD,CAAhB;AACD;;AACD,aAAOD,GAAP;AACD,KATyB,EAU1B,EAAE,GAAGT;AAAL,KAV0B,CAUZ;AAVY,KAA5B;;AAYA,UAAMa,IAAI,GAAGnB,MAAM,EAAnB;;AACA,UAAMoB,kBAAkB,GAAGpB,MAAM,EAAjC;;AACAD,IAAAA,mBAAmB,CACjBW,GADiB,EAEjB;AACA,UAAM;AACJ,YAAMW,IAAI,GAAGD,kBAAkB,CAACE,OAAhC,CADI,CAEJ;;AACA,UAAIH,IAAI,CAACG,OAAL,IAAgBD,IAApB,EAA0B;AACxB;AACAF,QAAAA,IAAI,CAACG,OAAL,CAAaC,UAAb,GAA0BF,IAAI,CAACE,UAA/B;AACA,eAAOJ,IAAI,CAACG,OAAZ;AACD;;AACD,aAAO,IAAP;AACD,KAZgB,EAajB,CAACH,IAAD,EAAOC,kBAAP,CAbiB,CAAnB;AAeA,wBACE,oBAAC,wBAAD,eACMT,mBADN;AAEE;AACA,MAAA,GAAG,EAAES;AAHP,qBAIE,oBAAC,SAAD,eAAeX,KAAf;AAAsB,MAAA,GAAG,EAAEU;AAA3B,OAJF,CADF;AAQD,GA1CwB,CAAzB,CADA,CA6CA;;AACAZ,EAAAA,gBAAgB,CAACiB,WAAjB,GACE,CAAAnB,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAEmB,WAAX,OACA;AACAnB,EAAAA,SAFA,aAEAA,SAFA,4CAEAA,SAAS,CAAEoB,MAFX,sDAEA,kBAAmBC,IAFnB,KAGC,OAAOrB,SAAP,KAAqB,QAArB,IAAiCA,SAHlC,IAIA,kBALF;AAOA,SAAOE,gBAAP;AACD", "sourcesContent": ["import * as React from 'react';\nimport { useImperativeHandle, useRef } from 'react';\n\nimport {\n  NativeViewGestureHandler,\n  NativeViewGestureHandlerProps,\n  nativeViewProps,\n} from './NativeViewGestureHandler';\n\n/*\n * This array should consist of:\n *   - All keys in propTypes from NativeGestureHandler\n *     (and all keys in GestureHandlerPropTypes)\n *   - 'onGestureHandlerEvent'\n *   - 'onGestureHandlerStateChange'\n */\nconst NATIVE_WRAPPER_PROPS_FILTER = [\n  ...nativeViewProps,\n  'onGestureHandlerEvent',\n  'onGestureHandlerStateChange',\n] as const;\n\nexport default function createNativeWrapper<P>(\n  Component: React.ComponentType<P>,\n  config: Readonly<NativeViewGestureHandlerProps> = {}\n) {\n  const ComponentWrapper = React.forwardRef<\n    React.ComponentType<any>,\n    P & NativeViewGestureHandlerProps\n  >((props, ref) => {\n    // Filter out props that should be passed to gesture handler wrapper\n    const gestureHandlerProps = Object.keys(props).reduce(\n      (res, key) => {\n        // TS being overly protective with it's types, see https://github.com/microsoft/TypeScript/issues/26255#issuecomment-458013731 for more info\n        const allowedKeys: readonly string[] = NATIVE_WRAPPER_PROPS_FILTER;\n        if (allowedKeys.includes(key)) {\n          // @ts-ignore FIXME(TS)\n          res[key] = props[key];\n        }\n        return res;\n      },\n      { ...config } // Watch out not to modify config\n    );\n    const _ref = useRef<React.ComponentType<P>>();\n    const _gestureHandlerRef = useRef<React.ComponentType<P>>();\n    useImperativeHandle(\n      ref,\n      // @ts-ignore TODO(TS) decide how nulls work in this context\n      () => {\n        const node = _gestureHandlerRef.current;\n        // Add handlerTag for relations config\n        if (_ref.current && node) {\n          // @ts-ignore FIXME(TS) think about createHandler return type\n          _ref.current.handlerTag = node.handlerTag;\n          return _ref.current;\n        }\n        return null;\n      },\n      [_ref, _gestureHandlerRef]\n    );\n    return (\n      <NativeViewGestureHandler\n        {...gestureHandlerProps}\n        // @ts-ignore TODO(TS)\n        ref={_gestureHandlerRef}>\n        <Component {...props} ref={_ref} />\n      </NativeViewGestureHandler>\n    );\n  });\n\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n  ComponentWrapper.displayName =\n    Component?.displayName ||\n    // @ts-ignore if render doesn't exist it will return undefined and go further\n    Component?.render?.name ||\n    (typeof Component === 'string' && Component) ||\n    'ComponentWrapper';\n\n  return ComponentWrapper;\n}\n"]}