{"version": 3, "file": "semconvStability.js", "sourceRoot": "", "sources": ["../../src/semconvStability.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,MAAM,CAAN,IAAY,gBAOX;AAPD,WAAY,gBAAgB;IAC1B,6CAA6C;IAC7C,2DAAY,CAAA;IACZ,0CAA0C;IAC1C,qDAAS,CAAA;IACT,qDAAqD;IACrD,iEAAqB,CAAA;AACvB,CAAC,EAPW,gBAAgB,KAAhB,gBAAgB,QAO3B;AAWD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CG;AACH,MAAM,UAAU,uBAAuB,CACrC,SAAoC,EACpC,GAAuB;IAEvB,IAAI,gBAAgB,GAAG,gBAAgB,CAAC,GAAG,CAAC;IAE5C,yEAAyE;IACzE,MAAM,OAAO,GAAG,GAAG;QACjB,EAAE,KAAK,CAAC,GAAG,CAAC;SACX,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;SAClB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;IACzB,KAAK,MAAM,KAAK,IAAI,OAAO,IAAI,EAAE,EAAE;QACjC,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,SAAS,GAAG,MAAM,EAAE;YAC9C,sCAAsC;YACtC,gBAAgB,GAAG,gBAAgB,CAAC,SAAS,CAAC;YAC9C,MAAM;SACP;aAAM,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,SAAS,EAAE;YAC5C,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC;SAC5C;KACF;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport enum SemconvStability {\n  /** Emit only stable semantic conventions. */\n  STABLE = 0x1,\n  /** Emit only old semantic conventions. */\n  OLD = 0x2,\n  /** Emit both stable and old semantic conventions. */\n  DUPLICATE = 0x1 | 0x2,\n}\n\n// Common namespaces mentioned in semantic-conventions docs, but allow\n// other custom strings.\ntype SemConvStabilityNamespace =\n  | 'http'\n  | 'messaging'\n  | 'database'\n  | 'k8s'\n  | (string & {});\n\n/**\n * Determine the appropriate semconv stability for the given namespace.\n *\n * This will parse the given string of comma-separated values (often\n * `process.env.OTEL_SEMCONV_STABILITY_OPT_IN`) looking for the `${namespace}`\n * or `${namespace}/dup` tokens. This is a pattern defined by a number of\n * non-normative semconv documents.\n *\n * For example:\n * - namespace 'http': https://opentelemetry.io/docs/specs/semconv/non-normative/http-migration/\n * - namespace 'database': https://opentelemetry.io/docs/specs/semconv/non-normative/database-migration/\n * - namespace 'k8s': https://opentelemetry.io/docs/specs/semconv/non-normative/k8s-migration/\n *\n * Usage:\n *\n *  import {SemconvStability, semconvStabilityFromStr} from '@opentelemetry/instrumentation';\n *\n *  export class FooInstrumentation extends InstrumentationBase<FooInstrumentationConfig> {\n *    private _semconvStability: SemconvStability;\n *    constructor(config: FooInstrumentationConfig = {}) {\n *      super('@opentelemetry/instrumentation-foo', VERSION, config);\n *\n *      // When supporting the OTEL_SEMCONV_STABILITY_OPT_IN envvar\n *      this._semconvStability = semconvStabilityFromStr(\n *        'http',\n *        process.env.OTEL_SEMCONV_STABILITY_OPT_IN\n *      );\n *\n *      // or when supporting a `semconvStabilityOptIn` config option (e.g. for\n *      // the web where there are no envvars).\n *      this._semconvStability = semconvStabilityFromStr(\n *        'http',\n *        config?.semconvStabilityOptIn\n *      );\n *    }\n *  }\n *\n *  // Then, to apply semconv, use the following or similar:\n *  if (this._semconvStability & SemconvStability.OLD) {\n *    // ...\n *  }\n *  if (this._semconvStability & SemconvStability.STABLE) {\n *    // ...\n *  }\n *\n */\nexport function semconvStabilityFromStr(\n  namespace: SemConvStabilityNamespace,\n  str: string | undefined\n) {\n  let semconvStability = SemconvStability.OLD;\n\n  // The same parsing of `str` as `getStringListFromEnv` from the core pkg.\n  const entries = str\n    ?.split(',')\n    .map(v => v.trim())\n    .filter(s => s !== '');\n  for (const entry of entries ?? []) {\n    if (entry.toLowerCase() === namespace + '/dup') {\n      // DUPLICATE takes highest precedence.\n      semconvStability = SemconvStability.DUPLICATE;\n      break;\n    } else if (entry.toLowerCase() === namespace) {\n      semconvStability = SemconvStability.STABLE;\n    }\n  }\n\n  return semconvStability;\n}\n"]}