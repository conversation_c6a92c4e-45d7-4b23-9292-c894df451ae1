{"version": 3, "sources": ["PinchGestureHandler.ts"], "names": ["PinchGestureHandler", "Gesture<PERSON>andler", "DEFAULT_TOUCH_SLOP", "onScaleBegin", "detector", "startingSpan", "getCurrentSpan", "onScale", "prevScaleFactor", "scale", "getScaleFactor", "tracker", "getTrackedPointersCount", "delta", "getTimeDelta", "velocity", "Math", "abs", "spanSlop", "currentState", "State", "BEGAN", "activate", "onScaleEnd", "_detector", "ScaleGestureDetector", "scaleDetectorListener", "init", "ref", "propsRef", "setShouldCancelWhenOutside", "updateGestureConfig", "enabled", "props", "transformNativeEvent", "focalX", "scaleGestureDetector", "getFocusX", "focalY", "getFocusY", "onPointerDown", "event", "addToTracker", "tryToSendTouchEvent", "onPointerAdd", "tryBegin", "onTouchEvent", "onPointerUp", "removeFromTracker", "pointerId", "ACTIVE", "end", "fail", "onPointerRemove", "onPointerMove", "track", "onPointerOutOfBounds", "UNDETERMINED", "resetProgress", "begin", "force", "onReset"], "mappings": ";;;;;;;AAAA;;AACA;;AAGA;;AACA;;;;;;AAIe,MAAMA,mBAAN,SAAkCC,uBAAlC,CAAiD;AAAA;AAAA;;AAAA,mCAC9C,CAD8C;;AAAA,sCAE3C,CAF2C;;AAAA,0CAIvC,CAJuC;;AAAA,sCAK3CC,6BAL2C;;AAAA,mDAOR;AACpDC,MAAAA,YAAY,EAAGC,QAAD,IAA6C;AACzD,aAAKC,YAAL,GAAoBD,QAAQ,CAACE,cAAT,EAApB;AACA,eAAO,IAAP;AACD,OAJmD;AAKpDC,MAAAA,OAAO,EAAGH,QAAD,IAA6C;AACpD,cAAMI,eAAuB,GAAG,KAAKC,KAArC;AACA,aAAKA,KAAL,IAAcL,QAAQ,CAACM,cAAT,CACZ,KAAKC,OAAL,CAAaC,uBAAb,EADY,CAAd;AAIA,cAAMC,KAAK,GAAGT,QAAQ,CAACU,YAAT,EAAd;;AACA,YAAID,KAAK,GAAG,CAAZ,EAAe;AACb,eAAKE,QAAL,GAAgB,CAAC,KAAKN,KAAL,GAAaD,eAAd,IAAiCK,KAAjD;AACD;;AAED,YACEG,IAAI,CAACC,GAAL,CAAS,KAAKZ,YAAL,GAAoBD,QAAQ,CAACE,cAAT,EAA7B,KACE,KAAKY,QADP,IAEA,KAAKC,YAAL,KAAsBC,aAAMC,KAH9B,EAIE;AACA,eAAKC,QAAL;AACD;;AACD,eAAO,IAAP;AACD,OAxBmD;AAyBpDC,MAAAA,UAAU,EACRC,SADU,IAGD,CAAE;AA5BuC,KAPQ;;AAAA,kDAsCT,IAAIC,6BAAJ,CACnD,KAAKC,qBAD8C,CAtCS;AAAA;;AA0CvDC,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAkD;AAC3D,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AAEA,SAAKC,0BAAL,CAAgC,KAAhC;AACD;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;AACD;;AAESC,EAAAA,oBAAoB,GAAG;AAC/B,WAAO;AACLC,MAAAA,MAAM,EAAE,KAAKC,oBAAL,CAA0BC,SAA1B,EADH;AAELC,MAAAA,MAAM,EAAE,KAAKF,oBAAL,CAA0BG,SAA1B,EAFH;AAGLxB,MAAAA,QAAQ,EAAE,KAAKA,QAHV;AAILN,MAAAA,KAAK,EAAE,KAAKA;AAJP,KAAP;AAMD;;AAES+B,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,SAAK9B,OAAL,CAAa+B,YAAb,CAA0BD,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AAEA,SAAKE,mBAAL,CAAyBF,KAAzB;AACD;;AAESG,EAAAA,YAAY,CAACH,KAAD,EAA4B;AAChD,SAAK9B,OAAL,CAAa+B,YAAb,CAA0BD,KAA1B;AACA,UAAMG,YAAN,CAAmBH,KAAnB;AACA,SAAKI,QAAL;AACA,SAAKT,oBAAL,CAA0BU,YAA1B,CAAuCL,KAAvC,EAA8C,KAAK9B,OAAnD;AACD;;AAESoC,EAAAA,WAAW,CAACN,KAAD,EAA4B;AAC/C,UAAMM,WAAN,CAAkBN,KAAlB;AACA,SAAK9B,OAAL,CAAaqC,iBAAb,CAA+BP,KAAK,CAACQ,SAArC;;AACA,QAAI,KAAK9B,YAAL,KAAsBC,aAAM8B,MAAhC,EAAwC;AACtC;AACD;;AACD,SAAKd,oBAAL,CAA0BU,YAA1B,CAAuCL,KAAvC,EAA8C,KAAK9B,OAAnD;;AAEA,QAAI,KAAKQ,YAAL,KAAsBC,aAAM8B,MAAhC,EAAwC;AACtC,WAAKC,GAAL;AACD,KAFD,MAEO;AACL,WAAKC,IAAL;AACD;AACF;;AAESC,EAAAA,eAAe,CAACZ,KAAD,EAA4B;AACnD,UAAMY,eAAN,CAAsBZ,KAAtB;AACA,SAAKL,oBAAL,CAA0BU,YAA1B,CAAuCL,KAAvC,EAA8C,KAAK9B,OAAnD;AACA,SAAKA,OAAL,CAAaqC,iBAAb,CAA+BP,KAAK,CAACQ,SAArC;;AAEA,QACE,KAAK9B,YAAL,KAAsBC,aAAM8B,MAA5B,IACA,KAAKvC,OAAL,CAAaC,uBAAb,KAAyC,CAF3C,EAGE;AACA,WAAKuC,GAAL;AACD;AACF;;AAESG,EAAAA,aAAa,CAACb,KAAD,EAA4B;AACjD,QAAI,KAAK9B,OAAL,CAAaC,uBAAb,KAAyC,CAA7C,EAAgD;AAC9C;AACD;;AACD,SAAKD,OAAL,CAAa4C,KAAb,CAAmBd,KAAnB;AAEA,SAAKL,oBAAL,CAA0BU,YAA1B,CAAuCL,KAAvC,EAA8C,KAAK9B,OAAnD;AACA,UAAM2C,aAAN,CAAoBb,KAApB;AACD;;AACSe,EAAAA,oBAAoB,CAACf,KAAD,EAA4B;AACxD,QAAI,KAAK9B,OAAL,CAAaC,uBAAb,KAAyC,CAA7C,EAAgD;AAC9C;AACD;;AACD,SAAKD,OAAL,CAAa4C,KAAb,CAAmBd,KAAnB;AAEA,SAAKL,oBAAL,CAA0BU,YAA1B,CAAuCL,KAAvC,EAA8C,KAAK9B,OAAnD;AACA,UAAM6C,oBAAN,CAA2Bf,KAA3B;AACD;;AAEOI,EAAAA,QAAQ,GAAS;AACvB,QAAI,KAAK1B,YAAL,KAAsBC,aAAMqC,YAAhC,EAA8C;AAC5C;AACD;;AAED,SAAKC,aAAL;AACA,SAAKC,KAAL;AACD;;AAEMrC,EAAAA,QAAQ,CAACsC,KAAD,EAAwB;AACrC,QAAI,KAAKzC,YAAL,KAAsBC,aAAM8B,MAAhC,EAAwC;AACtC,WAAKQ,aAAL;AACD;;AAED,UAAMpC,QAAN,CAAesC,KAAf;AACD;;AAESC,EAAAA,OAAO,GAAS;AACxB,SAAKH,aAAL;AACD;;AAESA,EAAAA,aAAa,GAAS;AAC9B,QAAI,KAAKvC,YAAL,KAAsBC,aAAM8B,MAAhC,EAAwC;AACtC;AACD;;AACD,SAAKnC,QAAL,GAAgB,CAAhB;AACA,SAAKN,KAAL,GAAa,CAAb;AACD;;AArJ6D", "sourcesContent": ["import { State } from '../../State';\nimport { DEFAULT_TOUCH_SLOP } from '../constants';\nimport { AdaptedEvent, Config } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\nimport ScaleGestureDetector, {\n  ScaleGestureListener,\n} from '../detectors/ScaleGestureDetector';\n\nexport default class PinchGestureHandler extends GestureHandler {\n  private scale = 1;\n  private velocity = 0;\n\n  private startingSpan = 0;\n  private spanSlop = DEFAULT_TOUCH_SLOP;\n\n  private scaleDetectorListener: ScaleGestureListener = {\n    onScaleBegin: (detector: ScaleGestureDetector): boolean => {\n      this.startingSpan = detector.getCurrentSpan();\n      return true;\n    },\n    onScale: (detector: ScaleGestureDetector): boolean => {\n      const prevScaleFactor: number = this.scale;\n      this.scale *= detector.getScaleFactor(\n        this.tracker.getTrackedPointersCount()\n      );\n\n      const delta = detector.getTimeDelta();\n      if (delta > 0) {\n        this.velocity = (this.scale - prevScaleFactor) / delta;\n      }\n\n      if (\n        Math.abs(this.startingSpan - detector.getCurrentSpan()) >=\n          this.spanSlop &&\n        this.currentState === State.BEGAN\n      ) {\n        this.activate();\n      }\n      return true;\n    },\n    onScaleEnd: (\n      _detector: ScaleGestureDetector\n      // eslint-disable-next-line @typescript-eslint/no-empty-function\n    ): void => {},\n  };\n\n  private scaleGestureDetector: ScaleGestureDetector = new ScaleGestureDetector(\n    this.scaleDetectorListener\n  );\n\n  public init(ref: number, propsRef: React.RefObject<unknown>) {\n    super.init(ref, propsRef);\n\n    this.setShouldCancelWhenOutside(false);\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n  }\n\n  protected transformNativeEvent() {\n    return {\n      focalX: this.scaleGestureDetector.getFocusX(),\n      focalY: this.scaleGestureDetector.getFocusY(),\n      velocity: this.velocity,\n      scale: this.scale,\n    };\n  }\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n\n    this.tryToSendTouchEvent(event);\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n    this.tryBegin();\n    this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.tracker.removeFromTracker(event.pointerId);\n    if (this.currentState !== State.ACTIVE) {\n      return;\n    }\n    this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n\n    if (this.currentState === State.ACTIVE) {\n      this.end();\n    } else {\n      this.fail();\n    }\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n    this.tracker.removeFromTracker(event.pointerId);\n\n    if (\n      this.currentState === State.ACTIVE &&\n      this.tracker.getTrackedPointersCount() < 2\n    ) {\n      this.end();\n    }\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    if (this.tracker.getTrackedPointersCount() < 2) {\n      return;\n    }\n    this.tracker.track(event);\n\n    this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n    super.onPointerMove(event);\n  }\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    if (this.tracker.getTrackedPointersCount() < 2) {\n      return;\n    }\n    this.tracker.track(event);\n\n    this.scaleGestureDetector.onTouchEvent(event, this.tracker);\n    super.onPointerOutOfBounds(event);\n  }\n\n  private tryBegin(): void {\n    if (this.currentState !== State.UNDETERMINED) {\n      return;\n    }\n\n    this.resetProgress();\n    this.begin();\n  }\n\n  public activate(force?: boolean): void {\n    if (this.currentState !== State.ACTIVE) {\n      this.resetProgress();\n    }\n\n    super.activate(force);\n  }\n\n  protected onReset(): void {\n    this.resetProgress();\n  }\n\n  protected resetProgress(): void {\n    if (this.currentState === State.ACTIVE) {\n      return;\n    }\n    this.velocity = 0;\n    this.scale = 1;\n  }\n}\n"]}