{"version": 3, "sources": ["LongPressGestureHandler.ts"], "names": ["Hammer", "State", "PressGestureHandler", "isnan", "isValidNumber", "LongPressGestureHandler", "minDurationMs", "config", "maxDist", "updateHasCustomActivationCriteria", "maxDistSq", "getConfig", "hasCustomActivationCriteria", "shouldCancelWhenOutside", "getHammerConfig", "time", "getState", "type", "INPUT_START", "ACTIVE", "INPUT_MOVE", "INPUT_END", "END", "INPUT_CANCEL", "FAILED"], "mappings": "AAAA;;AACA;AACA,OAAOA,MAAP,MAAmB,gBAAnB;AAEA,SAASC,KAAT,QAAsB,UAAtB;AACA,OAAOC,mBAAP,MAAgC,uBAAhC;AACA,SAASC,KAAT,EAAgBC,aAAhB,QAAqC,SAArC;;AAIA,MAAMC,uBAAN,SAAsCH,mBAAtC,CAA0D;AACvC,MAAbI,aAAa,GAAW;AAC1B;AACA,WAAOH,KAAK,CAAC,KAAKI,MAAL,CAAYD,aAAb,CAAL,GAAmC,GAAnC,GAAyC,KAAKC,MAAL,CAAYD,aAA5D;AACD;;AAEU,MAAPE,OAAO,GAAG;AACZ;AACA,WAAOL,KAAK,CAAC,KAAKI,MAAL,CAAYC,OAAb,CAAL,GAA6B,CAA7B,GAAiC,KAAKD,MAAL,CAAYC,OAApD;AACD;;AAEDC,EAAAA,iCAAiC,CAAC;AAAEC,IAAAA;AAAF,GAAD,EAAwB;AACvD,WAAO,CAACN,aAAa,CAACM,SAAD,CAArB;AACD;;AAEDC,EAAAA,SAAS,GAAG;AACV,QAAI,CAAC,KAAKC,2BAAV,EAAuC;AACrC;AACA;AACA,aAAO;AACLC,QAAAA,uBAAuB,EAAE,IADpB;AAELH,QAAAA,SAAS,EAAE;AAFN,OAAP;AAID;;AACD,WAAO,KAAKH,MAAZ;AACD;;AAEDO,EAAAA,eAAe,GAAG;AAChB,WAAO,EACL,GAAG,MAAMA,eAAN,EADE;AAEL;AACAC,MAAAA,IAAI,EAAE,KAAKT;AAHN,KAAP;AAKD;;AAEDU,EAAAA,QAAQ,CAACC,IAAD,EAAsC;AAC5C,WAAO;AACL,OAACjB,MAAM,CAACkB,WAAR,GAAsBjB,KAAK,CAACkB,MADvB;AAEL,OAACnB,MAAM,CAACoB,UAAR,GAAqBnB,KAAK,CAACkB,MAFtB;AAGL,OAACnB,MAAM,CAACqB,SAAR,GAAoBpB,KAAK,CAACqB,GAHrB;AAIL,OAACtB,MAAM,CAACuB,YAAR,GAAuBtB,KAAK,CAACuB;AAJxB,MAKLP,IALK,CAAP;AAMD;;AA1CuD;;AA6C1D,eAAeZ,uBAAf", "sourcesContent": ["/* eslint-disable eslint-comments/no-unlimited-disable */\n/* eslint-disable */\nimport Hammer from '@egjs/hammerjs';\n\nimport { State } from '../State';\nimport PressGestureHandler from './PressGestureHandler';\nimport { isnan, isValidNumber } from './utils';\nimport { Config } from './GestureHandler';\nimport { HammerInputNames } from './constants';\n\nclass LongPressGestureHandler extends PressGestureHandler {\n  get minDurationMs(): number {\n    // @ts-ignore FIXNE(TS)\n    return isnan(this.config.minDurationMs) ? 251 : this.config.minDurationMs;\n  }\n\n  get maxDist() {\n    // @ts-ignore FIXNE(TS)\n    return isnan(this.config.maxDist) ? 9 : this.config.maxDist;\n  }\n\n  updateHasCustomActivationCriteria({ maxDistSq }: Config) {\n    return !isValidNumber(maxDistSq);\n  }\n\n  getConfig() {\n    if (!this.hasCustomActivationCriteria) {\n      // Default config\n      // If no params have been defined then this config should emulate the native gesture as closely as possible.\n      return {\n        shouldCancelWhenOutside: true,\n        maxDistSq: 10,\n      };\n    }\n    return this.config;\n  }\n\n  getHammerConfig() {\n    return {\n      ...super.getHammerConfig(),\n      // threshold: this.maxDist,\n      time: this.minDurationMs,\n    };\n  }\n\n  getState(type: keyof typeof HammerInputNames) {\n    return {\n      [Hammer.INPUT_START]: State.ACTIVE,\n      [Hammer.INPUT_MOVE]: State.ACTIVE,\n      [Hammer.INPUT_END]: State.END,\n      [Hammer.INPUT_CANCEL]: State.FAILED,\n    }[type];\n  }\n}\n\nexport default LongPressGestureHandler;\n"]}