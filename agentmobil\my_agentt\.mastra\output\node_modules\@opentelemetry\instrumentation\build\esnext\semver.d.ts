/** Interface for the options to configure semantic versioning satisfy check. */
export interface SatisfiesOptions {
    /**
     * If set to true, the pre-release checks will be included
     * as described [here](https://github.com/npm/node-semver#prerelease-tags).
     */
    includePrerelease?: boolean;
}
/**
 * Checks given version whether it satisfies given range expression.
 * @param version the [version](https://github.com/npm/node-semver#versions) to be checked
 * @param range   the [range](https://github.com/npm/node-semver#ranges) expression for version check
 * @param options options to configure semver satisfy check
 */
export declare function satisfies(version: string, range: string, options?: SatisfiesOptions): boolean;
//# sourceMappingURL=semver.d.ts.map