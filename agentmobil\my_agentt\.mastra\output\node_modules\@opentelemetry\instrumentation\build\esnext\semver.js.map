{"version": 3, "file": "semver.js", "sourceRoot": "", "sources": ["../../src/semver.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,0EAA0E;AAC1E,gFAAgF;AAChF,+DAA+D;AAC/D,EAAE;AACF,8DAA8D;AAC9D,mFAAmF;AACnF,0EAA0E;AAC1E,iGAAiG;AAEjG,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,MAAM,cAAc,GAClB,oPAAoP,CAAC;AACvP,MAAM,YAAY,GAChB,oTAAoT,CAAC;AAEvT,MAAM,cAAc,GAA+B;IACjD,GAAG,EAAE,CAAC,CAAC,CAAC;IACR,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,GAAG,EAAE,CAAC,CAAC,CAAC;IACR,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;CACd,CAAC;AA2BF;;;;;GAKG;AACH,MAAM,UAAU,SAAS,CACvB,OAAe,EACf,KAAa,EACb,OAA0B;IAE1B,6BAA6B;IAC7B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE;QAC9B,IAAI,CAAC,KAAK,CAAC,oBAAoB,OAAO,EAAE,CAAC,CAAC;QAC1C,OAAO,KAAK,CAAC;KACd;IAED,uEAAuE;IACvE,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,IAAI,CAAC;KACb;IAED,gBAAgB;IAChB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAE9C,gBAAgB;IAChB,MAAM,aAAa,GAA8B,aAAa,CAAC,OAAO,CAAC,CAAC;IACxE,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO,KAAK,CAAC;KACd;IAED,MAAM,eAAe,GAAoB,EAAE,CAAC;IAE5C,kEAAkE;IAClE,MAAM,WAAW,GAAY,YAAY,CACvC,aAAa,EACb,KAAK,EACL,eAAe,EACf,OAAO,CACR,CAAC;IAEF,yBAAyB;IACzB,qFAAqF;IACrF,IAAI,WAAW,IAAI,CAAC,OAAO,EAAE,iBAAiB,EAAE;QAC9C,OAAO,gBAAgB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;KACzD;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAgB;IACxC,OAAO,OAAO,OAAO,KAAK,QAAQ,IAAI,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACrE,CAAC;AAED,SAAS,YAAY,CACnB,aAA4B,EAC5B,KAAa,EACb,eAAgC,EAChC,OAA0B;IAE1B,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACxB,2CAA2C;QAC3C,mGAAmG;QACnG,MAAM,MAAM,GAAa,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;YACtB,IAAI,WAAW,CAAC,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,OAAO,CAAC,EAAE;gBAC3D,OAAO,IAAI,CAAC;aACb;SACF;QACD,OAAO,KAAK,CAAC;KACd;SAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAChC,4EAA4E;QAC5E,KAAK,GAAG,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;KACvC;SAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC9B,sEAAsE;QACtE,MAAM,MAAM,GAAa,KAAK;aAC3B,IAAI,EAAE;aACN,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;aACvB,KAAK,CAAC,GAAG,CAAC,CAAC;QACd,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;YACtB,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,OAAO,CAAC,EAAE;gBAC5D,OAAO,KAAK,CAAC;aACd;SACF;QACD,OAAO,IAAI,CAAC;KACb;IAED,8CAA8C;IAC9C,OAAO,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;AACrE,CAAC;AAED,SAAS,WAAW,CAClB,aAA4B,EAC5B,KAAa,EACb,eAAgC,EAChC,OAA0B;IAE1B,KAAK,GAAG,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACxC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACvB,+DAA+D;QAC/D,OAAO,YAAY,CAAC,aAAa,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;KACrE;SAAM;QACL,2BAA2B;QAC3B,MAAM,WAAW,GAAkB,WAAW,CAAC,KAAK,CAAC,CAAC;QACtD,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAClC,uCAAuC;QACvC,OAAO,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;KAC/C;AACH,CAAC;AAED,SAAS,UAAU,CACjB,aAA4B,EAC5B,WAA0B;IAE1B,4DAA4D;IAC5D,IAAI,WAAW,CAAC,OAAO,EAAE;QACvB,OAAO,KAAK,CAAC;KACd;IAED,mFAAmF;IACnF,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;QAC5D,OAAO,IAAI,CAAC;KACb;IAED,gCAAgC;IAChC,IAAI,gBAAgB,GAAW,uBAAuB,CACpD,aAAa,CAAC,eAAe,IAAI,EAAE,EACnC,WAAW,CAAC,eAAe,IAAI,EAAE,CAClC,CAAC;IAEF,kEAAkE;IAClE,IAAI,gBAAgB,KAAK,CAAC,EAAE;QAC1B,MAAM,yBAAyB,GAC7B,aAAa,CAAC,kBAAkB,IAAI,EAAE,CAAC;QACzC,MAAM,uBAAuB,GAC3B,WAAW,CAAC,kBAAkB,IAAI,EAAE,CAAC;QACvC,IAAI,CAAC,yBAAyB,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE;YACxE,gBAAgB,GAAG,CAAC,CAAC;SACtB;aAAM,IACL,CAAC,yBAAyB,CAAC,MAAM;YACjC,uBAAuB,CAAC,MAAM,EAC9B;YACA,gBAAgB,GAAG,CAAC,CAAC;SACtB;aAAM,IACL,yBAAyB,CAAC,MAAM;YAChC,CAAC,uBAAuB,CAAC,MAAM,EAC/B;YACA,gBAAgB,GAAG,CAAC,CAAC,CAAC;SACvB;aAAM;YACL,gBAAgB,GAAG,uBAAuB,CACxC,yBAAyB,EACzB,uBAAuB,CACxB,CAAC;SACH;KACF;IAED,wDAAwD;IACxD,OAAO,cAAc,CAAC,WAAW,CAAC,EAAG,CAAC,EAAE,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AACrE,CAAC;AAED,SAAS,gBAAgB,CACvB,aAA4B,EAC5B,eAAgC;IAEhC,IAAI,aAAa,CAAC,UAAU,EAAE;QAC5B,OAAO,eAAe,CAAC,IAAI,CACzB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,OAAO,KAAK,aAAa,CAAC,OAAO,CACzD,CAAC;KACH;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,eAAe,CAAC,KAAa,EAAE,OAA0B;IAChE,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;IACrB,KAAK,GAAG,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACrC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IAC5B,KAAK,GAAG,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACtC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;IACrB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,GAAG,CAAC,EAAW;IACtB,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG,CAAC;AACvD,CAAC;AAED,SAAS,aAAa,CAAC,aAAqB;IAC1C,MAAM,KAAK,GAA4B,aAAa,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IAC3E,IAAI,CAAC,KAAK,EAAE;QACV,IAAI,CAAC,KAAK,CAAC,oBAAoB,aAAa,EAAE,CAAC,CAAC;QAChD,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,OAAO,GAAW,KAAM,CAAC,MAAO,CAAC,OAAO,CAAC;IAC/C,MAAM,UAAU,GAAW,KAAM,CAAC,MAAO,CAAC,UAAU,CAAC;IACrD,MAAM,KAAK,GAAW,KAAM,CAAC,MAAO,CAAC,KAAK,CAAC;IAE3C,MAAM,eAAe,GAAa,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrD,MAAM,kBAAkB,GAAyB,UAAU,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;IAExE,OAAO;QACL,EAAE,EAAE,SAAS;QAEb,OAAO;QACP,eAAe;QACf,mBAAmB,EAAE,eAAe,CAAC,MAAM;QAE3C,UAAU;QACV,kBAAkB;QAClB,sBAAsB,EAAE,kBAAkB,CAAC,CAAC,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE1E,KAAK;KACN,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,WAAmB;IACtC,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,EAAE,CAAC;KACX;IAED,MAAM,KAAK,GAA4B,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IACvE,IAAI,CAAC,KAAK,EAAE;QACV,IAAI,CAAC,KAAK,CAAC,kBAAkB,WAAW,EAAE,CAAC,CAAC;QAC5C,OAAO;YACL,OAAO,EAAE,IAAI;SACd,CAAC;KACH;IAED,IAAI,EAAE,GAAW,KAAM,CAAC,MAAO,CAAC,EAAE,CAAC;IACnC,MAAM,OAAO,GAAW,KAAM,CAAC,MAAO,CAAC,OAAO,CAAC;IAC/C,MAAM,UAAU,GAAW,KAAM,CAAC,MAAO,CAAC,UAAU,CAAC;IACrD,MAAM,KAAK,GAAW,KAAM,CAAC,MAAO,CAAC,KAAK,CAAC;IAE3C,MAAM,eAAe,GAAa,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrD,MAAM,kBAAkB,GAAyB,UAAU,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;IAExE,IAAI,EAAE,KAAK,IAAI,EAAE;QACf,EAAE,GAAG,GAAG,CAAC;KACV;IAED,OAAO;QACL,EAAE,EAAE,EAAE,IAAI,GAAG;QAEb,OAAO;QACP,eAAe;QACf,mBAAmB,EAAE,eAAe,CAAC,MAAM;QAE3C,UAAU;QACV,kBAAkB;QAClB,sBAAsB,EAAE,kBAAkB,CAAC,CAAC,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE1E,KAAK;KACN,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,CAAqB;IACxC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;AAC7C,CAAC;AAED,SAAS,mBAAmB,CAAC,CAAS;IACpC,MAAM,CAAC,GAAW,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAClC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC;AAED,SAAS,qBAAqB,CAC5B,CAAkB,EAClB,CAAkB;IAElB,IAAI,OAAO,CAAC,KAAK,OAAO,CAAC,EAAE;QACzB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACzB,OAAO,CAAC,CAAW,EAAE,CAAW,CAAC,CAAC;SACnC;aAAM,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YAChC,OAAO,CAAC,CAAW,EAAE,CAAW,CAAC,CAAC;SACnC;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACpE;KACF;SAAM;QACL,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/B;AACH,CAAC;AAED,SAAS,sBAAsB,CAAC,EAAU,EAAE,EAAU;IACpD,IAAI,WAAW,CAAC,EAAE,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC,EAAE;QACtC,OAAO,CAAC,CAAC;KACV;IACD,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,qBAAqB,CAChD,mBAAmB,CAAC,EAAE,CAAC,EACvB,mBAAmB,CAAC,EAAE,CAAC,CACxB,CAAC;IACF,IAAI,QAAQ,GAAG,QAAQ,EAAE;QACvB,OAAO,CAAC,CAAC;KACV;SAAM,IAAI,QAAQ,GAAG,QAAQ,EAAE;QAC9B,OAAO,CAAC,CAAC,CAAC;KACX;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,uBAAuB,CAAC,EAAY,EAAE,EAAY;IACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;QACvD,MAAM,GAAG,GAAW,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;QACvE,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,OAAO,GAAG,CAAC;SACZ;KACF;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED,gFAAgF;AAChF,oGAAoG;AACpG,WAAW;AACX;;;;;;;;;;;;;;;;GAgBG;AAEH,MAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,MAAM,iBAAiB,GAAG,aAAa,CAAC;AACxC,MAAM,oBAAoB,GAAG,gBAAgB,gBAAgB,GAAG,CAAC;AACjE,MAAM,IAAI,GAAG,cAAc,CAAC;AAE5B,MAAM,oBAAoB,GAAG,MAAM,iBAAiB,IAAI,oBAAoB,GAAG,CAAC;AAChF,MAAM,UAAU,GAAG,QAAQ,oBAAoB,SAAS,oBAAoB,MAAM,CAAC;AAEnF,MAAM,eAAe,GAAG,GAAG,gBAAgB,GAAG,CAAC;AAC/C,MAAM,KAAK,GAAG,UAAU,eAAe,SAAS,eAAe,MAAM,CAAC;AAEtE,MAAM,gBAAgB,GAAG,GAAG,iBAAiB,UAAU,CAAC;AACxD,MAAM,WAAW,GACf,YAAY,gBAAgB,GAAG;IAC/B,UAAU,gBAAgB,GAAG;IAC7B,UAAU,gBAAgB,GAAG;IAC7B,MAAM,UAAU,KAAK,KAAK,GAAG;IAC7B,MAAM,CAAC;AACT,MAAM,MAAM,GAAG,IAAI,IAAI,OAAO,WAAW,GAAG,CAAC;AAC7C,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC;AAEzC,MAAM,WAAW,GACf,SAAS,WAAW,GAAG,GAAG,WAAW,GAAG,IAAI,WAAW,GAAG,GAAG,OAAO,CAAC;AACvE,MAAM,kBAAkB,GAAG,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC;AAEnD,MAAM,SAAS,GAAG,SAAS,CAAC;AAC5B,MAAM,KAAK,GAAG,IAAI,SAAS,GAAG,WAAW,GAAG,CAAC;AAC7C,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;AAEvC,MAAM,SAAS,GAAG,SAAS,CAAC;AAC5B,MAAM,KAAK,GAAG,IAAI,SAAS,GAAG,WAAW,GAAG,CAAC;AAC7C,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;AAEvC,uHAAuH;AACvH,EAAE;AACF,iCAAiC;AACjC,4DAA4D;AAC5D,oDAAoD;AACpD,oDAAoD;AACpD,uCAAuC;AACvC,uCAAuC;AACvC,8BAA8B;AAC9B,SAAS,YAAY,CAAC,IAAY;IAChC,MAAM,CAAC,GAAG,YAAY,CAAC;IACvB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;QACxC,IAAI,GAAG,CAAC;QAER,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;YACV,GAAG,GAAG,EAAE,CAAC;SACV;aAAM,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;YACjB,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC;SACrC;aAAM,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;YACjB,2BAA2B;YAC3B,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;SAC3C;aAAM,IAAI,EAAE,EAAE;YACb,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;SACpD;aAAM;YACL,6BAA6B;YAC7B,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;SAC9C;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED,uHAAuH;AACvH,EAAE;AACF,6BAA6B;AAC7B,wCAAwC;AACxC,oCAAoC;AACpC,oCAAoC;AACpC,8BAA8B;AAC9B,8BAA8B;AAC9B,8BAA8B;AAC9B,8BAA8B;AAC9B,SAAS,YAAY,CAAC,IAAY,EAAE,OAA0B;IAC5D,MAAM,CAAC,GAAG,YAAY,CAAC;IACvB,MAAM,CAAC,GAAG,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IACjD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;QACxC,IAAI,GAAG,CAAC;QAER,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;YACV,GAAG,GAAG,EAAE,CAAC;SACV;aAAM,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;YACjB,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC;SACzC;aAAM,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;YACjB,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;aAC/C;iBAAM;gBACL,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC;aAC5C;SACF;aAAM,IAAI,EAAE,EAAE;YACb,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,IAAI,CAAC,KAAK,GAAG,EAAE;oBACb,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;iBACvD;qBAAM;oBACL,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;iBACpD;aACF;iBAAM;gBACL,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC;aACjD;SACF;aAAM;YACL,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,IAAI,CAAC,KAAK,GAAG,EAAE;oBACb,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;iBACrD;qBAAM;oBACL,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;iBAClD;aACF;iBAAM;gBACL,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC;aAC3C;SACF;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED,uHAAuH;AACvH,SAAS,aAAa,CAAC,IAAY,EAAE,OAA0B;IAC7D,MAAM,CAAC,GAAG,aAAa,CAAC;IACxB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;QAChD,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAClB,MAAM,EAAE,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,EAAE,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,IAAI,GAAG,EAAE,CAAC;QAEhB,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,EAAE;YACxB,IAAI,GAAG,EAAE,CAAC;SACX;QAED,4DAA4D;QAC5D,0DAA0D;QAC1D,EAAE,GAAG,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAE5C,IAAI,EAAE,EAAE;YACN,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,EAAE;gBAChC,qBAAqB;gBACrB,GAAG,GAAG,UAAU,CAAC;aAClB;iBAAM;gBACL,uBAAuB;gBACvB,GAAG,GAAG,GAAG,CAAC;aACX;SACF;aAAM,IAAI,IAAI,IAAI,IAAI,EAAE;YACvB,uDAAuD;YACvD,mBAAmB;YACnB,IAAI,EAAE,EAAE;gBACN,CAAC,GAAG,CAAC,CAAC;aACP;YACD,CAAC,GAAG,CAAC,CAAC;YAEN,IAAI,IAAI,KAAK,GAAG,EAAE;gBAChB,gBAAgB;gBAChB,kBAAkB;gBAClB,IAAI,GAAG,IAAI,CAAC;gBACZ,IAAI,EAAE,EAAE;oBACN,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACX,CAAC,GAAG,CAAC,CAAC;oBACN,CAAC,GAAG,CAAC,CAAC;iBACP;qBAAM;oBACL,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACX,CAAC,GAAG,CAAC,CAAC;iBACP;aACF;iBAAM,IAAI,IAAI,KAAK,IAAI,EAAE;gBACxB,qDAAqD;gBACrD,mDAAmD;gBACnD,IAAI,GAAG,GAAG,CAAC;gBACX,IAAI,EAAE,EAAE;oBACN,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;iBACZ;qBAAM;oBACL,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;iBACZ;aACF;YAED,IAAI,IAAI,KAAK,GAAG,EAAE;gBAChB,EAAE,GAAG,IAAI,CAAC;aACX;YAED,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;SACpC;aAAM,IAAI,EAAE,EAAE;YACb,GAAG,GAAG,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC;SAC1C;aAAM,IAAI,EAAE,EAAE;YACb,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;SAChD;QAED,OAAO,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED,uHAAuH;AACvH,EAAE;AACF,iCAAiC;AACjC,oDAAoD;AACpD,gCAAgC;AAChC,SAAS,aAAa,CAAC,IAAY,EAAE,OAA0B;IAC7D,MAAM,CAAC,GAAG,kBAAkB,CAAC;IAC7B,OAAO,IAAI,CAAC,OAAO,CACjB,CAAC,EACD,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE;QACpD,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE;YACX,IAAI,GAAG,EAAE,CAAC;SACX;aAAM,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE;YAClB,IAAI,GAAG,KAAK,EAAE,OAAO,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;SAC/D;aAAM,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE;YAClB,IAAI,GAAG,KAAK,EAAE,IAAI,EAAE,KAAK,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;SACnE;aAAM,IAAI,GAAG,EAAE;YACd,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;SACpB;aAAM;YACL,IAAI,GAAG,KAAK,IAAI,GAAG,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;SAC7D;QAED,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE;YACX,EAAE,GAAG,EAAE,CAAC;SACT;aAAM,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE;YAClB,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC;SAC1B;aAAM,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE;YAClB,EAAE,GAAG,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;SAC9B;aAAM,IAAI,GAAG,EAAE;YACd,EAAE,GAAG,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC;SACnC;aAAM,IAAI,OAAO,EAAE,iBAAiB,EAAE;YACrC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC;SAClC;aAAM;YACL,EAAE,GAAG,KAAK,EAAE,EAAE,CAAC;SAChB;QAED,OAAO,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;IAChC,CAAC,CACF,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// This is a custom semantic versioning implementation compatible with the\n// `satisfies(version, range, options?)` function from the `semver` npm package;\n// with the exception that the `loose` option is not supported.\n//\n// The motivation for the custom semver implementation is that\n// `semver` package has some initialization delay (lots of RegExp init and compile)\n// and this leads to coldstart overhead for the OTEL Lambda Node.js layer.\n// Hence, we have implemented lightweight version of it internally with required functionalities.\n\nimport { diag } from '@opentelemetry/api';\n\nconst VERSION_REGEXP =\n  /^(?:v)?(?<version>(?<major>0|[1-9]\\d*)\\.(?<minor>0|[1-9]\\d*)\\.(?<patch>0|[1-9]\\d*))(?:-(?<prerelease>(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+(?<build>[0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/;\nconst RANGE_REGEXP =\n  /^(?<op><|>|=|==|<=|>=|~|\\^|~>)?\\s*(?:v)?(?<version>(?<major>x|X|\\*|0|[1-9]\\d*)(?:\\.(?<minor>x|X|\\*|0|[1-9]\\d*))?(?:\\.(?<patch>x|X|\\*|0|[1-9]\\d*))?)(?:-(?<prerelease>(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+(?<build>[0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/;\n\nconst operatorResMap: { [op: string]: number[] } = {\n  '>': [1],\n  '>=': [0, 1],\n  '=': [0],\n  '<=': [-1, 0],\n  '<': [-1],\n  '!=': [-1, 1],\n};\n\n/** Interface for the options to configure semantic versioning satisfy check. */\nexport interface SatisfiesOptions {\n  /**\n   * If set to true, the pre-release checks will be included\n   * as described [here](https://github.com/npm/node-semver#prerelease-tags).\n   */\n  includePrerelease?: boolean;\n}\n\ninterface ParsedVersion {\n  op?: string;\n\n  version?: string;\n  versionSegments?: string[];\n  versionSegmentCount?: number;\n\n  prerelease?: string;\n  prereleaseSegments?: string[];\n  prereleaseSegmentCount?: number;\n\n  build?: string;\n\n  invalid?: boolean;\n}\n\n/**\n * Checks given version whether it satisfies given range expression.\n * @param version the [version](https://github.com/npm/node-semver#versions) to be checked\n * @param range   the [range](https://github.com/npm/node-semver#ranges) expression for version check\n * @param options options to configure semver satisfy check\n */\nexport function satisfies(\n  version: string,\n  range: string,\n  options?: SatisfiesOptions\n): boolean {\n  // Strict semver format check\n  if (!_validateVersion(version)) {\n    diag.error(`Invalid version: ${version}`);\n    return false;\n  }\n\n  // If range is empty, satisfy check succeeds regardless what version is\n  if (!range) {\n    return true;\n  }\n\n  // Cleanup range\n  range = range.replace(/([<>=~^]+)\\s+/g, '$1');\n\n  // Parse version\n  const parsedVersion: ParsedVersion | undefined = _parseVersion(version);\n  if (!parsedVersion) {\n    return false;\n  }\n\n  const allParsedRanges: ParsedVersion[] = [];\n\n  // Check given version whether it satisfies given range expression\n  const checkResult: boolean = _doSatisfies(\n    parsedVersion,\n    range,\n    allParsedRanges,\n    options\n  );\n\n  // If check result is OK,\n  // do another final check for pre-release, if pre-release check is included by option\n  if (checkResult && !options?.includePrerelease) {\n    return _doPreleaseCheck(parsedVersion, allParsedRanges);\n  }\n  return checkResult;\n}\n\nfunction _validateVersion(version: unknown): boolean {\n  return typeof version === 'string' && VERSION_REGEXP.test(version);\n}\n\nfunction _doSatisfies(\n  parsedVersion: ParsedVersion,\n  range: string,\n  allParsedRanges: ParsedVersion[],\n  options?: SatisfiesOptions\n): boolean {\n  if (range.includes('||')) {\n    // A version matches a range if and only if\n    // every comparator in at least one of the ||-separated comparator sets is satisfied by the version\n    const ranges: string[] = range.trim().split('||');\n    for (const r of ranges) {\n      if (_checkRange(parsedVersion, r, allParsedRanges, options)) {\n        return true;\n      }\n    }\n    return false;\n  } else if (range.includes(' - ')) {\n    // Hyphen ranges: https://github.com/npm/node-semver#hyphen-ranges-xyz---abc\n    range = replaceHyphen(range, options);\n  } else if (range.includes(' ')) {\n    // Multiple separated ranges and all needs to be satisfied for success\n    const ranges: string[] = range\n      .trim()\n      .replace(/\\s{2,}/g, ' ')\n      .split(' ');\n    for (const r of ranges) {\n      if (!_checkRange(parsedVersion, r, allParsedRanges, options)) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  // Check given parsed version with given range\n  return _checkRange(parsedVersion, range, allParsedRanges, options);\n}\n\nfunction _checkRange(\n  parsedVersion: ParsedVersion,\n  range: string,\n  allParsedRanges: ParsedVersion[],\n  options?: SatisfiesOptions\n): boolean {\n  range = _normalizeRange(range, options);\n  if (range.includes(' ')) {\n    // If there are multiple ranges separated, satisfy each of them\n    return _doSatisfies(parsedVersion, range, allParsedRanges, options);\n  } else {\n    // Validate and parse range\n    const parsedRange: ParsedVersion = _parseRange(range);\n    allParsedRanges.push(parsedRange);\n    // Check parsed version by parsed range\n    return _satisfies(parsedVersion, parsedRange);\n  }\n}\n\nfunction _satisfies(\n  parsedVersion: ParsedVersion,\n  parsedRange: ParsedVersion\n): boolean {\n  // If range is invalid, satisfy check fails (no error throw)\n  if (parsedRange.invalid) {\n    return false;\n  }\n\n  // If range is empty or wildcard, satisfy check succeeds regardless what version is\n  if (!parsedRange.version || _isWildcard(parsedRange.version)) {\n    return true;\n  }\n\n  // Compare version segment first\n  let comparisonResult: number = _compareVersionSegments(\n    parsedVersion.versionSegments || [],\n    parsedRange.versionSegments || []\n  );\n\n  // If versions segments are equal, compare by pre-release segments\n  if (comparisonResult === 0) {\n    const versionPrereleaseSegments: string[] =\n      parsedVersion.prereleaseSegments || [];\n    const rangePrereleaseSegments: string[] =\n      parsedRange.prereleaseSegments || [];\n    if (!versionPrereleaseSegments.length && !rangePrereleaseSegments.length) {\n      comparisonResult = 0;\n    } else if (\n      !versionPrereleaseSegments.length &&\n      rangePrereleaseSegments.length\n    ) {\n      comparisonResult = 1;\n    } else if (\n      versionPrereleaseSegments.length &&\n      !rangePrereleaseSegments.length\n    ) {\n      comparisonResult = -1;\n    } else {\n      comparisonResult = _compareVersionSegments(\n        versionPrereleaseSegments,\n        rangePrereleaseSegments\n      );\n    }\n  }\n\n  // Resolve check result according to comparison operator\n  return operatorResMap[parsedRange.op!]?.includes(comparisonResult);\n}\n\nfunction _doPreleaseCheck(\n  parsedVersion: ParsedVersion,\n  allParsedRanges: ParsedVersion[]\n): boolean {\n  if (parsedVersion.prerelease) {\n    return allParsedRanges.some(\n      r => r.prerelease && r.version === parsedVersion.version\n    );\n  }\n  return true;\n}\n\nfunction _normalizeRange(range: string, options?: SatisfiesOptions): string {\n  range = range.trim();\n  range = replaceCaret(range, options);\n  range = replaceTilde(range);\n  range = replaceXRange(range, options);\n  range = range.trim();\n  return range;\n}\n\nfunction isX(id?: string): boolean {\n  return !id || id.toLowerCase() === 'x' || id === '*';\n}\n\nfunction _parseVersion(versionString: string): ParsedVersion | undefined {\n  const match: RegExpMatchArray | null = versionString.match(VERSION_REGEXP);\n  if (!match) {\n    diag.error(`Invalid version: ${versionString}`);\n    return undefined;\n  }\n\n  const version: string = match!.groups!.version;\n  const prerelease: string = match!.groups!.prerelease;\n  const build: string = match!.groups!.build;\n\n  const versionSegments: string[] = version.split('.');\n  const prereleaseSegments: string[] | undefined = prerelease?.split('.');\n\n  return {\n    op: undefined,\n\n    version,\n    versionSegments,\n    versionSegmentCount: versionSegments.length,\n\n    prerelease,\n    prereleaseSegments,\n    prereleaseSegmentCount: prereleaseSegments ? prereleaseSegments.length : 0,\n\n    build,\n  };\n}\n\nfunction _parseRange(rangeString: string): ParsedVersion {\n  if (!rangeString) {\n    return {};\n  }\n\n  const match: RegExpMatchArray | null = rangeString.match(RANGE_REGEXP);\n  if (!match) {\n    diag.error(`Invalid range: ${rangeString}`);\n    return {\n      invalid: true,\n    };\n  }\n\n  let op: string = match!.groups!.op;\n  const version: string = match!.groups!.version;\n  const prerelease: string = match!.groups!.prerelease;\n  const build: string = match!.groups!.build;\n\n  const versionSegments: string[] = version.split('.');\n  const prereleaseSegments: string[] | undefined = prerelease?.split('.');\n\n  if (op === '==') {\n    op = '=';\n  }\n\n  return {\n    op: op || '=',\n\n    version,\n    versionSegments,\n    versionSegmentCount: versionSegments.length,\n\n    prerelease,\n    prereleaseSegments,\n    prereleaseSegmentCount: prereleaseSegments ? prereleaseSegments.length : 0,\n\n    build,\n  };\n}\n\nfunction _isWildcard(s: string | undefined): boolean {\n  return s === '*' || s === 'x' || s === 'X';\n}\n\nfunction _parseVersionString(v: string): string | number {\n  const n: number = parseInt(v, 10);\n  return isNaN(n) ? v : n;\n}\n\nfunction _normalizeVersionType(\n  a: string | number,\n  b: string | number\n): [string, string] | [number, number] {\n  if (typeof a === typeof b) {\n    if (typeof a === 'number') {\n      return [a as number, b as number];\n    } else if (typeof a === 'string') {\n      return [a as string, b as string];\n    } else {\n      throw new Error('Version segments can only be strings or numbers');\n    }\n  } else {\n    return [String(a), String(b)];\n  }\n}\n\nfunction _compareVersionStrings(v1: string, v2: string): number {\n  if (_isWildcard(v1) || _isWildcard(v2)) {\n    return 0;\n  }\n  const [parsedV1, parsedV2] = _normalizeVersionType(\n    _parseVersionString(v1),\n    _parseVersionString(v2)\n  );\n  if (parsedV1 > parsedV2) {\n    return 1;\n  } else if (parsedV1 < parsedV2) {\n    return -1;\n  }\n  return 0;\n}\n\nfunction _compareVersionSegments(v1: string[], v2: string[]): number {\n  for (let i = 0; i < Math.max(v1.length, v2.length); i++) {\n    const res: number = _compareVersionStrings(v1[i] || '0', v2[i] || '0');\n    if (res !== 0) {\n      return res;\n    }\n  }\n  return 0;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// The rest of this file is adapted from portions of https://github.com/npm/node-semver/tree/868d4bb\n// License:\n/*\n * The ISC License\n *\n * Copyright (c) Isaac Z. Schlueter and Contributors\n *\n * Permission to use, copy, modify, and/or distribute this software for any\n * purpose with or without fee is hereby granted, provided that the above\n * copyright notice and this permission notice appear in all copies.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\n * IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n */\n\nconst LETTERDASHNUMBER = '[a-zA-Z0-9-]';\nconst NUMERICIDENTIFIER = '0|[1-9]\\\\d*';\nconst NONNUMERICIDENTIFIER = `\\\\d*[a-zA-Z-]${LETTERDASHNUMBER}*`;\nconst GTLT = '((?:<|>)?=?)';\n\nconst PRERELEASEIDENTIFIER = `(?:${NUMERICIDENTIFIER}|${NONNUMERICIDENTIFIER})`;\nconst PRERELEASE = `(?:-(${PRERELEASEIDENTIFIER}(?:\\\\.${PRERELEASEIDENTIFIER})*))`;\n\nconst BUILDIDENTIFIER = `${LETTERDASHNUMBER}+`;\nconst BUILD = `(?:\\\\+(${BUILDIDENTIFIER}(?:\\\\.${BUILDIDENTIFIER})*))`;\n\nconst XRANGEIDENTIFIER = `${NUMERICIDENTIFIER}|x|X|\\\\*`;\nconst XRANGEPLAIN =\n  `[v=\\\\s]*(${XRANGEIDENTIFIER})` +\n  `(?:\\\\.(${XRANGEIDENTIFIER})` +\n  `(?:\\\\.(${XRANGEIDENTIFIER})` +\n  `(?:${PRERELEASE})?${BUILD}?` +\n  `)?)?`;\nconst XRANGE = `^${GTLT}\\\\s*${XRANGEPLAIN}$`;\nconst XRANGE_REGEXP = new RegExp(XRANGE);\n\nconst HYPHENRANGE =\n  `^\\\\s*(${XRANGEPLAIN})` + `\\\\s+-\\\\s+` + `(${XRANGEPLAIN})` + `\\\\s*$`;\nconst HYPHENRANGE_REGEXP = new RegExp(HYPHENRANGE);\n\nconst LONETILDE = '(?:~>?)';\nconst TILDE = `^${LONETILDE}${XRANGEPLAIN}$`;\nconst TILDE_REGEXP = new RegExp(TILDE);\n\nconst LONECARET = '(?:\\\\^)';\nconst CARET = `^${LONECARET}${XRANGEPLAIN}$`;\nconst CARET_REGEXP = new RegExp(CARET);\n\n// Borrowed from https://github.com/npm/node-semver/blob/868d4bbe3d318c52544f38d5f9977a1103e924c2/classes/range.js#L285\n//\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0-0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0-0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0-0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0-0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0-0\n// ~0.0.1 --> >=0.0.1 <0.1.0-0\nfunction replaceTilde(comp: string): string {\n  const r = TILDE_REGEXP;\n  return comp.replace(r, (_, M, m, p, pr) => {\n    let ret;\n\n    if (isX(M)) {\n      ret = '';\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0 <${+M + 1}.0.0-0`;\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0-0\n      ret = `>=${M}.${m}.0 <${M}.${+m + 1}.0-0`;\n    } else if (pr) {\n      ret = `>=${M}.${m}.${p}-${pr} <${M}.${+m + 1}.0-0`;\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0-0\n      ret = `>=${M}.${m}.${p} <${M}.${+m + 1}.0-0`;\n    }\n    return ret;\n  });\n}\n\n// Borrowed from https://github.com/npm/node-semver/blob/868d4bbe3d318c52544f38d5f9977a1103e924c2/classes/range.js#L329\n//\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0-0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0-0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0-0\n// ^1.2.3 --> >=1.2.3 <2.0.0-0\n// ^1.2.0 --> >=1.2.0 <2.0.0-0\n// ^0.0.1 --> >=0.0.1 <0.0.2-0\n// ^0.1.0 --> >=0.1.0 <0.2.0-0\nfunction replaceCaret(comp: string, options?: SatisfiesOptions): string {\n  const r = CARET_REGEXP;\n  const z = options?.includePrerelease ? '-0' : '';\n  return comp.replace(r, (_, M, m, p, pr) => {\n    let ret;\n\n    if (isX(M)) {\n      ret = '';\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0${z} <${+M + 1}.0.0-0`;\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = `>=${M}.${m}.0${z} <${M}.${+m + 1}.0-0`;\n      } else {\n        ret = `>=${M}.${m}.0${z} <${+M + 1}.0.0-0`;\n      }\n    } else if (pr) {\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p}-${pr} <${M}.${m}.${+p + 1}-0`;\n        } else {\n          ret = `>=${M}.${m}.${p}-${pr} <${M}.${+m + 1}.0-0`;\n        }\n      } else {\n        ret = `>=${M}.${m}.${p}-${pr} <${+M + 1}.0.0-0`;\n      }\n    } else {\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p}${z} <${M}.${m}.${+p + 1}-0`;\n        } else {\n          ret = `>=${M}.${m}.${p}${z} <${M}.${+m + 1}.0-0`;\n        }\n      } else {\n        ret = `>=${M}.${m}.${p} <${+M + 1}.0.0-0`;\n      }\n    }\n    return ret;\n  });\n}\n\n// Borrowed from https://github.com/npm/node-semver/blob/868d4bbe3d318c52544f38d5f9977a1103e924c2/classes/range.js#L390\nfunction replaceXRange(comp: string, options?: SatisfiesOptions): string {\n  const r = XRANGE_REGEXP;\n  return comp.replace(r, (ret, gtlt, M, m, p, pr) => {\n    const xM = isX(M);\n    const xm = xM || isX(m);\n    const xp = xm || isX(p);\n    const anyX = xp;\n\n    if (gtlt === '=' && anyX) {\n      gtlt = '';\n    }\n\n    // if we're including prereleases in the match, then we need\n    // to fix this to -0, the lowest possible prerelease value\n    pr = options?.includePrerelease ? '-0' : '';\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0-0';\n      } else {\n        // nothing is forbidden\n        ret = '*';\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0;\n      }\n      p = 0;\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        gtlt = '>=';\n        if (xm) {\n          M = +M + 1;\n          m = 0;\n          p = 0;\n        } else {\n          m = +m + 1;\n          p = 0;\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<';\n        if (xm) {\n          M = +M + 1;\n        } else {\n          m = +m + 1;\n        }\n      }\n\n      if (gtlt === '<') {\n        pr = '-0';\n      }\n\n      ret = `${gtlt + M}.${m}.${p}${pr}`;\n    } else if (xm) {\n      ret = `>=${M}.0.0${pr} <${+M + 1}.0.0-0`;\n    } else if (xp) {\n      ret = `>=${M}.${m}.0${pr} <${M}.${+m + 1}.0-0`;\n    }\n\n    return ret;\n  });\n}\n\n// Borrowed from https://github.com/npm/node-semver/blob/868d4bbe3d318c52544f38d5f9977a1103e924c2/classes/range.js#L488\n//\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0-0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0-0\nfunction replaceHyphen(comp: string, options?: SatisfiesOptions): string {\n  const r = HYPHENRANGE_REGEXP;\n  return comp.replace(\n    r,\n    (_, from, fM, fm, fp, fpr, fb, to, tM, tm, tp, tpr) => {\n      if (isX(fM)) {\n        from = '';\n      } else if (isX(fm)) {\n        from = `>=${fM}.0.0${options?.includePrerelease ? '-0' : ''}`;\n      } else if (isX(fp)) {\n        from = `>=${fM}.${fm}.0${options?.includePrerelease ? '-0' : ''}`;\n      } else if (fpr) {\n        from = `>=${from}`;\n      } else {\n        from = `>=${from}${options?.includePrerelease ? '-0' : ''}`;\n      }\n\n      if (isX(tM)) {\n        to = '';\n      } else if (isX(tm)) {\n        to = `<${+tM + 1}.0.0-0`;\n      } else if (isX(tp)) {\n        to = `<${tM}.${+tm + 1}.0-0`;\n      } else if (tpr) {\n        to = `<=${tM}.${tm}.${tp}-${tpr}`;\n      } else if (options?.includePrerelease) {\n        to = `<${tM}.${tm}.${+tp + 1}-0`;\n      } else {\n        to = `<=${to}`;\n      }\n\n      return `${from} ${to}`.trim();\n    }\n  );\n}\n"]}