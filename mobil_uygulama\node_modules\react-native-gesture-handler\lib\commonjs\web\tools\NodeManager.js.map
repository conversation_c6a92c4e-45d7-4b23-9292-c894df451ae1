{"version": 3, "sources": ["NodeManager.ts"], "names": ["NodeManager", "<PERSON><PERSON><PERSON><PERSON>", "tag", "gestures", "Error", "createGestureHandler", "handlerTag", "handler", "setTag", "dropGestureHandler", "onDestroy", "getNodes"], "mappings": ";;;;;;;;;AAIA;AACe,MAAeA,WAAf,CAA2B;AAMhB,SAAVC,UAAU,CAACC,GAAD,EAA+B;AACrD,QAAIA,GAAG,IAAI,KAAKC,QAAhB,EAA0B;AACxB,aAAO,KAAKA,QAAL,CAAcD,GAAd,CAAP;AACD;;AAED,UAAM,IAAIE,KAAJ,CAAW,sBAAqBF,GAAI,EAApC,CAAN;AACD;;AAEiC,SAApBG,oBAAoB,CAChCC,UADgC,EAEhCC,OAFgC,EAG1B;AACN,QAAID,UAAU,IAAI,KAAKH,QAAvB,EAAiC;AAC/B,YAAM,IAAIC,KAAJ,CACH,oBAAmBE,UAAW,mGAD3B,CAAN;AAGD;;AAED,SAAKH,QAAL,CAAcG,UAAd,IAA4BC,OAA5B;AACA,SAAKJ,QAAL,CAAcG,UAAd,EAA0BE,MAA1B,CAAiCF,UAAjC;AACD;;AAE+B,SAAlBG,kBAAkB,CAACH,UAAD,EAA2B;AACzD,QAAI,EAAEA,UAAU,IAAI,KAAKH,QAArB,CAAJ,EAAoC;AAClC;AACD;;AAED,SAAKA,QAAL,CAAcG,UAAd,EAA0BI,SAA1B,GALyD,CAOzD;;AACA,WAAO,KAAKP,QAAL,CAAcG,UAAd,CAAP;AACD;;AAEqB,SAARK,QAAQ,GAAG;AACvB,WAAO,EAAE,GAAG,KAAKR;AAAV,KAAP;AACD;;AAzCuC;;;;gBAAZH,W,cAIxB,E", "sourcesContent": ["import { ValueOf } from '../../typeUtils';\nimport { Gestures } from '../Gestures';\nimport type IGestureHandler from '../handlers/IGestureHandler';\n\n// eslint-disable-next-line @typescript-eslint/no-extraneous-class\nexport default abstract class NodeManager {\n  private static gestures: Record<\n    number,\n    InstanceType<ValueOf<typeof Gestures>>\n  > = {};\n\n  public static getHandler(tag: number): IGestureHandler {\n    if (tag in this.gestures) {\n      return this.gestures[tag] as IGestureHandler;\n    }\n\n    throw new Error(`No handler for tag ${tag}`);\n  }\n\n  public static createGestureHandler(\n    handlerTag: number,\n    handler: InstanceType<ValueOf<typeof Gestures>>\n  ): void {\n    if (handlerTag in this.gestures) {\n      throw new Error(\n        `Handler with tag ${handlerTag} already exists. Please ensure that no Gesture instance is used across multiple GestureDetectors.`\n      );\n    }\n\n    this.gestures[handlerTag] = handler;\n    this.gestures[handlerTag].setTag(handlerTag);\n  }\n\n  public static dropGestureHandler(handlerTag: number): void {\n    if (!(handlerTag in this.gestures)) {\n      return;\n    }\n\n    this.gestures[handlerTag].onDestroy();\n\n    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n    delete this.gestures[handlerTag];\n  }\n\n  public static getNodes() {\n    return { ...this.gestures };\n  }\n}\n"]}